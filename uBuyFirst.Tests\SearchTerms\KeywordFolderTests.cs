﻿using System.Windows.Forms;
using DevExpress.XtraTreeList;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Search;

namespace uBuyFirst.Tests.SearchTerms
{
    [TestClass]
    public class KeywordFolderTests
    {
        private KeywordFolder _testFolder;
        private QueryList _testQueryList;

        [TestInitialize]
        public void Setup()
        {
            _testFolder = new KeywordFolder
            {
                Name = "Test Folder",
                Description = "Test Description"
            };

            _testQueryList = new QueryList();
        }

        [TestMethod]
        public void KeywordFolder_Constructor_SetsDefaultValues()
        {
            // Arrange & Act
            var folder = new KeywordFolder();

            // Assert
            Assert.IsFalse(string.IsNullOrEmpty(folder.Id), "Folder ID should be auto-generated");
            Assert.IsTrue(folder.IsExpanded, "Folder should be expanded by default");
            Assert.IsNotNull(folder.Children, "Children collection should be initialized");
            Assert.IsNotNull(folder.Keywords, "Keywords collection should be initialized");
            Assert.AreEqual(0, folder.Children.Count, "Children collection should be empty");
            Assert.AreEqual(0, folder.Keywords.Count, "Keywords collection should be empty");
        }

        [TestMethod]
        public void KeywordFolder_SetName_UpdatesCorrectly()
        {
            // Arrange
            var expectedName = "Electronics";

            // Act
            _testFolder.Name = expectedName;

            // Assert
            Assert.AreEqual(expectedName, _testFolder.Name);
        }

        [TestMethod]
        public void KeywordFolder_GetFullPath_ReturnsCorrectPath()
        {
            // Arrange
            var rootFolder = new KeywordFolder { Name = "Electronics" };
            var subFolder = new KeywordFolder { Name = "Mobile Phones", ParentFolder = rootFolder };
            rootFolder.Children.Add(subFolder);

            // Act
            var path = subFolder.GetFullPath();

            // Assert
            Assert.AreEqual("Electronics > Mobile Phones", path);
        }

        [TestMethod]
        public void KeywordFolder_GetFullPath_RootFolder_ReturnsName()
        {
            // Arrange
            _testFolder.Name = "Root Folder";

            // Act
            var path = _testFolder.GetFullPath();

            // Assert
            Assert.AreEqual("Root Folder", path);
        }

        [TestMethod]
        public void KeywordFolder_GetAllKeywords_ReturnsKeywordsFromAllLevels()
        {
            // Arrange
            var keyword1 = new Keyword2Find { Alias = "Keyword 1" };
            var keyword2 = new Keyword2Find { Alias = "Keyword 2" };
            var keyword3 = new Keyword2Find { Alias = "Keyword 3" };

            var childFolder = new KeywordFolder { Name = "Child Folder" };
            childFolder.Keywords.Add(keyword3);

            _testFolder.Keywords.Add(keyword1);
            _testFolder.Keywords.Add(keyword2);
            _testFolder.Children.Add(childFolder);

            // Act
            var allKeywords = _testFolder.GetAllKeywords();

            // Assert
            Assert.AreEqual(3, allKeywords.Count);
            Assert.IsTrue(allKeywords.Contains(keyword1));
            Assert.IsTrue(allKeywords.Contains(keyword2));
            Assert.IsTrue(allKeywords.Contains(keyword3));
        }

        [TestMethod]
        public void KeywordFolder_CanAcceptDrop_Keyword_ReturnsTrue()
        {
            // Arrange
            var keyword = new Keyword2Find { Alias = "Test Keyword" };

            // Act
            var canAccept = _testFolder.CanAcceptDrop(keyword);

            // Assert
            Assert.IsTrue(canAccept);
        }

        [TestMethod]
        public void KeywordFolder_CanAcceptDrop_NonCircularFolder_ReturnsTrue()
        {
            // Arrange
            var otherFolder = new KeywordFolder { Name = "Other Folder" };

            // Act
            var canAccept = _testFolder.CanAcceptDrop(otherFolder);

            // Assert
            Assert.IsTrue(canAccept);
        }

        [TestMethod]
        public void KeywordFolder_CanAcceptDrop_CircularReference_ReturnsFalse()
        {
            // Arrange
            var parentFolder = new KeywordFolder { Name = "Parent" };
            var childFolder = new KeywordFolder { Name = "Child", ParentFolder = parentFolder };
            parentFolder.Children.Add(childFolder);

            // Act - try to drop parent into child (circular reference)
            var canAccept = childFolder.CanAcceptDrop(parentFolder);

            // Assert
            Assert.IsFalse(canAccept);
        }

        [TestMethod]
        public void KeywordFolder_AcceptDrop_Keyword_AddsToFolder()
        {
            // Arrange
            var keyword = new Keyword2Find { Alias = "Test Keyword" };

            // Act
            _testFolder.AcceptDrop(keyword);

            // Assert
            Assert.AreEqual(1, _testFolder.Keywords.Count);
            Assert.IsTrue(_testFolder.Keywords.Contains(keyword));
            Assert.AreEqual(_testFolder, keyword.ParentFolder);
        }

        [TestMethod]
        public void KeywordFolder_AcceptDrop_Folder_AddsToChildren()
        {
            // Arrange
            var childFolder = new KeywordFolder { Name = "Child Folder" };

            // Act
            _testFolder.AcceptDrop(childFolder);

            // Assert
            Assert.AreEqual(1, _testFolder.Children.Count);
            Assert.IsTrue(_testFolder.Children.Contains(childFolder));
            Assert.AreEqual(_testFolder, childFolder.ParentFolder);
        }

        [TestMethod]
        public void KeywordFolder_ValidateHierarchy_ValidStructure_ReturnsTrue()
        {
            // Arrange
            var childFolder = new KeywordFolder { Name = "Child", ParentFolder = _testFolder };
            _testFolder.Children.Add(childFolder);

            // Act
            var isValid = _testFolder.ValidateHierarchy();

            // Assert
            Assert.IsTrue(isValid);
        }

        [TestMethod]
        public void KeywordFolder_VirtualTreeGetCellValue_Alias_ReturnsName()
        {
            // Arrange
            _testFolder.Name = "Test Folder Name";
            var column = new DevExpress.XtraTreeList.Columns.TreeListColumn { Caption = "Alias" };
            var info = new VirtualTreeGetCellValueInfo(_testFolder, column);

            // Act
            _testFolder.VirtualTreeGetCellValue(info);

            // Assert
            Assert.AreEqual("Test Folder Name", info.CellData);
        }

        [TestMethod]
        public void KeywordFolder_VirtualTreeGetCellValue_Keywords_ReturnsCount()
        {
            // Arrange
            _testFolder.Keywords.Add(new Keyword2Find { Alias = "Keyword 1" });
            _testFolder.Keywords.Add(new Keyword2Find { Alias = "Keyword 2" });

            var column = new DevExpress.XtraTreeList.Columns.TreeListColumn { Caption = "Keywords" };
            var info = new VirtualTreeGetCellValueInfo(_testFolder, column);

            // Act
            _testFolder.VirtualTreeGetCellValue(info);

            // Assert
            Assert.AreEqual("[2 keywords]", info.CellData);
        }

        [TestMethod]
        public void KeywordFolder_VirtualTreeSetCellValue_Alias_UpdatesName()
        {
            // Arrange
            var newName = "Updated Folder Name";
            var column = new DevExpress.XtraTreeList.Columns.TreeListColumn { Caption = "Alias" };
            var info = new VirtualTreeSetCellValueInfo(_testFolder, "Old Name", newName, column);

            // Act
            _testFolder.VirtualTreeSetCellValue(info);

            // Assert
            Assert.AreEqual(newName, _testFolder.Name);
        }

        [TestMethod]
        public void KeywordFolder_GetFolderEnabledState_NoKeywords_ReturnsUnchecked()
        {
            // Arrange - folder with no keywords
            var column = new DevExpress.XtraTreeList.Columns.TreeListColumn { Caption = "Enabled" };
            var info = new VirtualTreeGetCellValueInfo(_testFolder, column);

            // Act
            _testFolder.VirtualTreeGetCellValue(info);

            // Assert
            Assert.AreEqual(CheckState.Unchecked, info.CellData);
        }

        [TestMethod]
        public void KeywordFolder_GetFolderEnabledState_AllKeywordsEnabled_ReturnsChecked()
        {
            // Arrange
            var keyword1 = new Keyword2Find { Alias = "Keyword 1", KeywordEnabled = CheckState.Checked };
            var keyword2 = new Keyword2Find { Alias = "Keyword 2", KeywordEnabled = CheckState.Checked };
            _testFolder.Keywords.Add(keyword1);
            _testFolder.Keywords.Add(keyword2);

            var column = new DevExpress.XtraTreeList.Columns.TreeListColumn { Caption = "Enabled" };
            var info = new VirtualTreeGetCellValueInfo(_testFolder, column);

            // Act
            _testFolder.VirtualTreeGetCellValue(info);

            // Assert
            Assert.AreEqual(CheckState.Checked, info.CellData);
        }

        [TestMethod]
        public void KeywordFolder_GetFolderEnabledState_AllKeywordsDisabled_ReturnsUnchecked()
        {
            // Arrange
            var keyword1 = new Keyword2Find { Alias = "Keyword 1", KeywordEnabled = CheckState.Unchecked };
            var keyword2 = new Keyword2Find { Alias = "Keyword 2", KeywordEnabled = CheckState.Unchecked };
            _testFolder.Keywords.Add(keyword1);
            _testFolder.Keywords.Add(keyword2);

            var column = new DevExpress.XtraTreeList.Columns.TreeListColumn { Caption = "Enabled" };
            var info = new VirtualTreeGetCellValueInfo(_testFolder, column);

            // Act
            _testFolder.VirtualTreeGetCellValue(info);

            // Assert
            Assert.AreEqual(CheckState.Unchecked, info.CellData);
        }

        [TestMethod]
        public void KeywordFolder_GetFolderEnabledState_MixedKeywords_ReturnsIndeterminate()
        {
            // Arrange
            var keyword1 = new Keyword2Find { Alias = "Keyword 1", KeywordEnabled = CheckState.Checked };
            var keyword2 = new Keyword2Find { Alias = "Keyword 2", KeywordEnabled = CheckState.Unchecked };
            _testFolder.Keywords.Add(keyword1);
            _testFolder.Keywords.Add(keyword2);

            var column = new DevExpress.XtraTreeList.Columns.TreeListColumn { Caption = "Enabled" };
            var info = new VirtualTreeGetCellValueInfo(_testFolder, column);

            // Act
            _testFolder.VirtualTreeGetCellValue(info);

            // Assert
            Assert.AreEqual(CheckState.Indeterminate, info.CellData);
        }

        [TestMethod]
        public void KeywordFolder_GetFolderEnabledState_IncludesSubfolderKeywords()
        {
            // Arrange
            var keyword1 = new Keyword2Find { Alias = "Keyword 1", KeywordEnabled = CheckState.Checked };
            var keyword2 = new Keyword2Find { Alias = "Keyword 2", KeywordEnabled = CheckState.Unchecked };

            var subFolder = new KeywordFolder { Name = "Sub Folder" };
            var keyword3 = new Keyword2Find { Alias = "Keyword 3", KeywordEnabled = CheckState.Checked };
            subFolder.Keywords.Add(keyword3);

            _testFolder.Keywords.Add(keyword1);
            _testFolder.Keywords.Add(keyword2);
            _testFolder.Children.Add(subFolder);

            var column = new DevExpress.XtraTreeList.Columns.TreeListColumn { Caption = "Enabled" };
            var info = new VirtualTreeGetCellValueInfo(_testFolder, column);

            // Act
            _testFolder.VirtualTreeGetCellValue(info);

            // Assert - 2 enabled, 1 disabled = indeterminate
            Assert.AreEqual(CheckState.Indeterminate, info.CellData);
        }
    }
}
