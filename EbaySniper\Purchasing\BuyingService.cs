﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net;
using uBuyFirst.Intl;
using uBuyFirst.Prefs;
using uBuyFirst.Pricing;

namespace uBuyFirst.Purchasing
{
    public class BuyingService
    {
        public enum OfferStatus
        {
            OfferNotStarted,
            OfferPageLoaded,
            OfferReviewNeeded,
            OfferReviewed,
            OfferSubmitting,
            OfferAuthInitializing,
            OfferConfirming,
            OfferSent,
            OfferFailed
        }

        /// <summary>
        /// Base abstract class for all types of orders
        /// </summary>
        public abstract class Order
        {
            // Common properties for all order types
            public string ShipToLocation { get; set; } // Stores extracted zipcode from eBay response
            public string ItemID { get; set; }
            public int Quantity { get; set; }
            public CurrencyAmount ItemPrice { get; set; }
            public Placeoffer.OrderAction OrderAction { get; set; }
            public EBaySite EbaySite { get; set; }
            public string OrderStatus { get; set; }
            public string UserName { get; set; }
            public Stopwatch _sw { get; set; }
            public IProgress<string> PurchaseProgress { get; set; }
            public CookieContainer? CookieContainer { get; set; }
            public string Title { get; set; }
            public bool IsRestockPurchase { get; set; }

            // Common checkout properties
            public string SessionID { get; set; }
            public bool IsNewCheckout { get; set; }
            public string SessionHtml { get; set; }
            public CheckoutState CheckoutStatus { get; set; }
            public bool AutoConfirmationAllowed { get; set; }

            // Common properties for authentication and payment
            public string? AddressID { get; set; }
            public string? LogisticsId { get; set; }
            public string? DisableSca { get; set; }
            public string? IsPurchaseEmbeddedInScaAction { get; set; }
            public OfferStatus CurrentOfferStatus { get; set; }
            public string? FailureReasonMessage { get; set; } // Moved from BestOfferOrder

            // PayPal specific properties - needed in base class as they're used in multiple places
            public string AccessToken { get; set; }
            public string Srt { get; set; }
            public string FundingID { get; set; }
            public string ClientMetadataId { get; set; }
            public string ClientID { get; set; }
            public string MerchantID { get; set; }
            public string CartTotal { get; set; }

            protected Order(string itemID, string title, EBaySite ebaySite, int quantity, CurrencyAmount itemPriceValue, Placeoffer.OrderAction orderAction)
            {
                EbaySite = ebaySite;
                ItemID = itemID;
                Title = title;
                Quantity = quantity;
                ItemPrice = itemPriceValue;
                OrderAction = orderAction;
                SessionID = "";
                SessionHtml = "";
                PurchaseProgress = new Progress<string>(s => { });
                _sw = new();
                CheckoutStatus = CheckoutState.NotStarted;
                CurrentOfferStatus = OfferStatus.OfferNotStarted;

                // Initialize authentication properties
                AddressID = null;
                LogisticsId = null;
                DisableSca = null;
                IsPurchaseEmbeddedInScaAction = null;

                // Initialize PayPal properties
                AccessToken = "";
                Srt = "";
                FundingID = "";
                ClientMetadataId = "";
                ClientID = "";
                MerchantID = "";
                CartTotal = "";
            }

            public enum CheckoutState
            {
                NotStarted,
                CreatingSession,
                SessionCreated,
                SessionCreationFailed,
                PaymentInProgress,
                PaymentSuccess,
                PaymentFailed,
                ConfirmationFailed,
                TestPurchase
            }
        }

        /// <summary>
        /// Regular buy order for immediate purchase
        /// </summary>
        public class BuyOrder : Order
        {
            // BuyOrder specific properties
            public object PlaceOfferAccount { get; set; }

            public BuyOrder(string itemID, string title, EBaySite ebaySite, int quantity, CurrencyAmount itemPriceValue,
                Placeoffer.OrderAction orderAction)
                : base(itemID, title, ebaySite, quantity, itemPriceValue, orderAction) // Add title here
            {
                // Initialize buy-specific properties if needed
            }
        }

        /// <summary>
        /// Best offer order for negotiated purchases
        /// </summary>
        public class BestOfferOrder : Order
        {
            // Best Offer specific properties
            public double OfferTotalPrice { get; set; }
            public string OfferCurrency { get; set; }
            public string OfferMessage { get; set; }
            public List<string?>? SrtTokens { get; set; }
            public string? CheckoutUrl { get; set; }
            public string? FinalOfferAction { get; set; }
            public string? FinalOfferAmount { get; set; }
            public string? Step3SrtToken { get; set; }
            public string? Step5SrtToken { get; set; }
            public string? PseudoOrderId { get; set; }
            public string? PaymentInstrumentId { get; set; }
            public string? PaymentInstrumentType { get; set; }
            public string? AdditionalParametersForSubmit { get; set; }
            public string? Step4SrtToken { get; set; }

            public string? SendOfferUrl { get; set; }
            public bool IsShortFlow { get; set; }
            public string? SendOfferPayload { get; set; }
            // public string? FailureReasonMessage { get; set; } // Moved to base Order class

            public BestOfferOrder(string itemID, string title, EBaySite ebaySite, int quantity, CurrencyAmount itemPriceValue, double offerTotalPrice, string offerCurrency, string offerMessage = "")
                : base(itemID, title, ebaySite, quantity, itemPriceValue, Placeoffer.OrderAction.BestOffer) // Add title here
            {
                OfferTotalPrice = offerTotalPrice;
                OfferCurrency = offerCurrency;
                OfferMessage = offerMessage;
                SrtTokens = new List<string?>();
                IsShortFlow = false; // Default to long flow
                // CurrentOfferStatus is already set in the base class constructor
            }
        }
    }
}
