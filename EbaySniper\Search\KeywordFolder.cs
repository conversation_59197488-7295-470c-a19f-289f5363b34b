﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Serialization;
using DevExpress.XtraTreeList;
using System.Reflection;
using System.Windows.Forms;

namespace uBuyFirst.Search
{
    [Serializable]
    [Obfuscation(Exclude = true)]
    [XmlInclude(typeof(Keyword2Find))]
    public class KeywordFolder : TreeList.IVirtualTreeListData
    {
        #region Core Properties

        /// <summary>
        /// Unique identifier for the folder
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Display name of the folder
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// Whether the folder is expanded in the TreeList
        /// </summary>
        public bool IsExpanded { get; set; } = true;

        /// <summary>
        /// Optional description for the folder
        /// </summary>
        public string Description { get; set; } = "";

        /// <summary>
        /// Creation timestamp
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        #endregion

        #region Hierarchy Relationships

        /// <summary>
        /// Parent folder (null for root-level folders)
        /// </summary>
        [XmlIgnore]
        public KeywordFolder ParentFolder { get; set; }

        /// <summary>
        /// Child folders (nested folders)
        /// </summary>
        public List<KeywordFolder> Children { get; set; } = new List<KeywordFolder>();

        /// <summary>
        /// Keywords contained in this folder
        /// </summary>
        public List<Keyword2Find> Keywords { get; set; } = new List<Keyword2Find>();

        #endregion

        #region TreeList Integration

        /// <summary>
        /// Provides child nodes for TreeList virtual mode
        /// </summary>
        public void VirtualTreeGetChildNodes(VirtualTreeGetChildNodesInfo info)
        {
            var children = new List<object>();
            children.AddRange(Children);      // Add child folders first
            children.AddRange(Keywords);      // Then add keywords
            info.Children = children;
        }

        /// <summary>
        /// Provides cell values for TreeList columns
        /// </summary>
        public void VirtualTreeGetCellValue(VirtualTreeGetCellValueInfo info)
        {
            switch (info.Column.Caption)
            {
                case "Enabled":
                    // Folder enabled state based on whether any keywords are enabled
                    info.CellData = GetFolderEnabledState();
                    break;
                case "Alias":
                    info.CellData = Name;
                    break;
                case "Keywords":
                    info.CellData = $"[{Keywords.Count} keywords]";
                    break;
                default:
                    info.CellData = ""; // Empty for other columns
                    break;
            }
        }

        /// <summary>
        /// Handles cell value changes (e.g., folder rename)
        /// </summary>
        public void VirtualTreeSetCellValue(VirtualTreeSetCellValueInfo info)
        {
            if (info.Column.Caption == "Alias" && info.NewCellData is string newName)
            {
                Name = newName;
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Gets the full path of the folder (e.g., "Electronics > Mobile Phones")
        /// </summary>
        public string GetFullPath()
        {
            if (ParentFolder == null) return Name;

            var pathParts = new List<string>();
            var current = this;

            while (current != null)
            {
                pathParts.Insert(0, current.Name);
                current = current.ParentFolder;
            }

            return string.Join(" > ", pathParts);
        }

        /// <summary>
        /// Gets all keywords in this folder and all subfolders
        /// </summary>
        public List<Keyword2Find> GetAllKeywords()
        {
            var allKeywords = new List<Keyword2Find>(Keywords);

            foreach (var childFolder in Children)
            {
                allKeywords.AddRange(childFolder.GetAllKeywords());
            }

            return allKeywords;
        }

        /// <summary>
        /// Gets all folders in this hierarchy (including this folder)
        /// </summary>
        public List<KeywordFolder> GetAllFolders()
        {
            var allFolders = new List<KeywordFolder> { this };

            foreach (var childFolder in Children)
            {
                allFolders.AddRange(childFolder.GetAllFolders());
            }

            return allFolders;
        }

        /// <summary>
        /// Checks if this folder can accept a dropped item
        /// </summary>
        public bool CanAcceptDrop(object draggedItem)
        {
            switch (draggedItem)
            {
                case Keyword2Find keyword:
                    return true; // Folders can always accept keywords
                case KeywordFolder folder:
                    return !folder.IsAncestorOf(this); // Prevent circular references
                default:
                    return false;
            }
        }

        /// <summary>
        /// Handles dropping an item into this folder
        /// </summary>
        public void AcceptDrop(object draggedItem, int position = -1)
        {
            switch (draggedItem)
            {
                case Keyword2Find keyword:
                    // Remove from current parent
                    keyword.ParentFolder?.Keywords.Remove(keyword);

                    // Add to this folder
                    if (position >= 0 && position < Keywords.Count)
                        Keywords.Insert(position, keyword);
                    else
                        Keywords.Add(keyword);

                    keyword.ParentFolder = this;
                    break;

                case KeywordFolder folder:
                    if (CanAcceptDrop(folder))
                    {
                        // Remove from current parent
                        folder.ParentFolder?.Children.Remove(folder);

                        // Add to this folder
                        if (position >= 0 && position < Children.Count)
                            Children.Insert(position, folder);
                        else
                            Children.Add(folder);

                        folder.ParentFolder = this;
                    }
                    break;
            }
        }

        /// <summary>
        /// Checks if this folder is an ancestor of the specified folder
        /// </summary>
        internal bool IsAncestorOf(KeywordFolder folder)
        {
            var current = folder.ParentFolder;
            while (current != null)
            {
                if (current == this) return true;
                current = current.ParentFolder;
            }
            return false;
        }

        /// <summary>
        /// Validates the folder hierarchy for circular references
        /// </summary>
        public bool ValidateHierarchy()
        {
            var visited = new HashSet<string>();
            return ValidateHierarchyRecursive(visited);
        }

        private bool ValidateHierarchyRecursive(HashSet<string> visited)
        {
            if (visited.Contains(Id)) return false; // Circular reference detected

            visited.Add(Id);

            foreach (var child in Children)
            {
                if (!child.ValidateHierarchyRecursive(new HashSet<string>(visited)))
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Gets the root QueryList by traversing up the hierarchy
        /// </summary>
        public QueryList GetRootQueryList()
        {
            // This would need to be implemented based on how the QueryList maintains references
            // For now, return null as this is primarily for backward compatibility
            return null;
        }

        /// <summary>
        /// Gets all sibling folders (folders at the same level)
        /// </summary>
        public List<KeywordFolder> GetSiblingFolders()
        {
            if (ParentFolder != null)
            {
                return ParentFolder.Children.Where(f => f != this).ToList();
            }
            else
            {
                // This is a root folder, need to get siblings from QueryList
                // For now, return empty list - this will be handled by the QueryList methods
                return new List<KeywordFolder>();
            }
        }

        /// <summary>
        /// Checks if a folder name already exists among siblings
        /// </summary>
        public bool IsNameTakenBySibling(string name)
        {
            var siblings = GetSiblingFolders();
            return siblings.Any(f => f.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Generates a unique folder name by adding numbers if needed
        /// </summary>
        public static string GenerateUniqueName(string baseName, List<KeywordFolder> existingFolders)
        {
            if (!existingFolders.Any(f => f.Name.Equals(baseName, StringComparison.OrdinalIgnoreCase)))
            {
                return baseName; // Name is already unique
            }

            int counter = 2;
            string uniqueName;

            do
            {
                uniqueName = $"{baseName} ({counter})";
                counter++;
            }
            while (existingFolders.Any(f => f.Name.Equals(uniqueName, StringComparison.OrdinalIgnoreCase)));

            return uniqueName;
        }

        /// <summary>
        /// Gets the enabled state of the folder based on its keywords
        /// </summary>
        private CheckState GetFolderEnabledState()
        {
            var allKeywords = GetAllKeywords();

            if (!allKeywords.Any())
                return CheckState.Unchecked; // No keywords = unchecked

            var enabledCount = allKeywords.Count(k => k.KeywordEnabled == CheckState.Checked);
            var totalCount = allKeywords.Count;

            if (enabledCount == 0)
                return CheckState.Unchecked; // No keywords enabled
            else if (enabledCount == totalCount)
                return CheckState.Checked; // All keywords enabled
            else
                return CheckState.Indeterminate; // Some keywords enabled
        }

        #endregion
    }
}
