﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Xml;
using System.Xml.Schema;
using System.Xml.Serialization;
using DevExpress.XtraTreeList;

namespace uBuyFirst.Search
{
    [Serializable]
    [Obfuscation(Exclude = true)]
    [XmlInclude(typeof(KeywordFolder))]
    public class QueryList : TreeList.IVirtualTreeListData, IXmlSerializable
    {
        public QueryList()
        {
        }

        #region Properties

        /// <summary>
        /// Root-level folders
        /// </summary>
        public readonly List<KeywordFolder> Folders = new List<KeywordFolder>();

        /// <summary>
        /// Backward compatibility property - returns all keywords from all folders
        /// </summary>
        [XmlIgnore]
        public List<Keyword2Find> ChildrenCore
        {
            get { return GetAllKeywords(); }
        }

        #endregion

        #region TreeList Integration

        /// <summary>
        /// Provides root nodes for TreeList (folders)
        /// </summary>
        public void VirtualTreeGetChildNodes(VirtualTreeGetChildNodesInfo info)
        {
            info.Children = Folders;
        }

        public void VirtualTreeGetCellValue(VirtualTreeGetCellValueInfo info)
        {
            // Not used at root level
        }

        public void VirtualTreeSetCellValue(VirtualTreeSetCellValueInfo info)
        {
            // Not used at root level
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Gets all keywords from all folders
        /// </summary>
        private List<Keyword2Find> GetAllKeywords()
        {
            var allKeywords = new List<Keyword2Find>();

            foreach (var folder in Folders)
            {
                allKeywords.AddRange(folder.GetAllKeywords());
            }

            return allKeywords;
        }

        /// <summary>
        /// Migrates from flat keyword structure to folder structure
        /// </summary>
        public void MigrateFromFlatStructure(List<Keyword2Find> existingKeywords)
        {
            if (!existingKeywords.Any()) return;

            // Find keywords that aren't in any folder yet
            var orphanedKeywords = existingKeywords.Where(kw => kw.ParentFolder == null).ToList();

            if (!orphanedKeywords.Any()) return;

            // Find or create default folder
            var defaultFolder = Folders.FirstOrDefault(f => f.Name == "Default");
            if (defaultFolder == null)
            {
                defaultFolder = new KeywordFolder
                {
                    Name = "Default",
                    Id = "default-folder"
                };
                Folders.Add(defaultFolder);
            }

            // Move orphaned keywords to default folder
            foreach (var keyword in orphanedKeywords)
            {
                keyword.ParentFolder = defaultFolder;
                if (!defaultFolder.Keywords.Contains(keyword))
                {
                    defaultFolder.Keywords.Add(keyword);
                }
            }
        }

        /// <summary>
        /// Finds a folder by its full path
        /// </summary>
        public KeywordFolder FindFolderByPath(string folderPath)
        {
            if (string.IsNullOrEmpty(folderPath)) return null;

            var pathParts = folderPath.Split(new[] { " > " }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var rootFolder in Folders)
            {
                var found = FindFolderByPathRecursive(rootFolder, pathParts, 0);
                if (found != null) return found;
            }

            return null;
        }

        private KeywordFolder FindFolderByPathRecursive(KeywordFolder folder, string[] pathParts, int index)
        {
            if (index >= pathParts.Length) return null;

            if (folder.Name.Equals(pathParts[index], StringComparison.OrdinalIgnoreCase))
            {
                if (index == pathParts.Length - 1) return folder; // Found target folder

                // Continue searching in children
                foreach (var child in folder.Children)
                {
                    var found = FindFolderByPathRecursive(child, pathParts, index + 1);
                    if (found != null) return found;
                }
            }

            return null;
        }

        /// <summary>
        /// Reconstructs folder structure from keywords with FolderPath information
        /// </summary>
        private void ReconstructFolderStructure(List<Keyword2Find> keywords)
        {
            Folders.Clear();

            // Group keywords by folder path
            var keywordsByFolder = keywords.GroupBy(k => k.FolderPath ?? "").ToList();

            foreach (var group in keywordsByFolder)
            {
                var folderPath = group.Key;
                var folderKeywords = group.ToList();

                KeywordFolder targetFolder;

                if (string.IsNullOrEmpty(folderPath))
                {
                    // Keywords without folder path go to default folder
                    targetFolder = Folders.FirstOrDefault(f => f.Name == "Default");
                    if (targetFolder == null)
                    {
                        targetFolder = new KeywordFolder
                        {
                            Name = "Default",
                            Id = "default-folder"
                        };
                        Folders.Add(targetFolder);
                    }
                }
                else
                {
                    // Find or create folder by path
                    targetFolder = FindFolderByPath(folderPath);
                    if (targetFolder == null)
                    {
                        targetFolder = CreateFolderByPath(folderPath);
                    }
                }

                // Add keywords to the target folder
                foreach (var keyword in folderKeywords)
                {
                    keyword.ParentCore = this;
                    keyword.ParentFolder = targetFolder;
                    if (!targetFolder.Keywords.Contains(keyword))
                    {
                        targetFolder.Keywords.Add(keyword);
                    }
                }
            }
        }

        /// <summary>
        /// Creates folder hierarchy from a folder path
        /// </summary>
        private KeywordFolder CreateFolderByPath(string folderPath)
        {
            var pathParts = folderPath.Split(new[] { " > " }, StringSplitOptions.RemoveEmptyEntries);
            KeywordFolder currentFolder = null;
            KeywordFolder parentFolder = null;

            for (int i = 0; i < pathParts.Length; i++)
            {
                var folderName = pathParts[i].Trim();
                var partialPath = string.Join(" > ", pathParts.Take(i + 1));

                currentFolder = FindFolderByPath(partialPath);
                if (currentFolder == null)
                {
                    // Generate unique folder name to avoid conflicts
                    string uniqueFolderName;
                    if (parentFolder != null)
                    {
                        uniqueFolderName = KeywordFolder.GenerateUniqueName(folderName, parentFolder.Children);
                    }
                    else
                    {
                        uniqueFolderName = GenerateUniqueRootFolderName(folderName);
                    }

                    currentFolder = new KeywordFolder
                    {
                        Name = uniqueFolderName,
                        Id = Guid.NewGuid().ToString(),
                        ParentFolder = parentFolder
                    };

                    if (parentFolder != null)
                    {
                        parentFolder.Children.Add(currentFolder);
                    }
                    else
                    {
                        Folders.Add(currentFolder);
                    }
                }

                parentFolder = currentFolder;
            }

            return currentFolder;
        }

        /// <summary>
        /// Checks if a folder name already exists at the root level
        /// </summary>
        public bool IsRootFolderNameTaken(string name)
        {
            return Folders.Any(f => f.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Generates a unique folder name at the root level
        /// </summary>
        public string GenerateUniqueRootFolderName(string baseName)
        {
            return KeywordFolder.GenerateUniqueName(baseName, Folders);
        }

        /// <summary>
        /// Checks if a folder name is taken by siblings at any level
        /// </summary>
        public bool IsFolderNameTakenBySiblings(KeywordFolder folder, string newName)
        {
            if (folder.ParentFolder != null)
            {
                // Check siblings in parent folder
                return folder.ParentFolder.Children
                    .Where(f => f != folder)
                    .Any(f => f.Name.Equals(newName, StringComparison.OrdinalIgnoreCase));
            }
            else
            {
                // Check root level siblings
                return Folders
                    .Where(f => f != folder)
                    .Any(f => f.Name.Equals(newName, StringComparison.OrdinalIgnoreCase));
            }
        }

        #endregion

        #region XML Serialization

        public XmlSchema GetSchema()
        {
            return null;
        }

        public void ReadXml(XmlReader reader)
        {
            if (reader.IsEmptyElement)
            {
                reader.Read();
                return;
            }

            reader.ReadStartElement();

            // Read all keywords first
            var allKeywords = new List<Keyword2Find>();

            while (reader.NodeType != XmlNodeType.EndElement)
            {
                if (reader.NodeType == XmlNodeType.Element && reader.LocalName == "Keywords")
                {
                    if (!reader.IsEmptyElement)
                    {
                        reader.ReadStartElement("Keywords");

                        while (reader.NodeType != XmlNodeType.EndElement)
                        {
                            if (reader.NodeType == XmlNodeType.Element && reader.LocalName == "Keyword2Find")
                            {
                                var keywordSerializer = new XmlSerializer(typeof(Keyword2Find));
                                var keywordReader = reader.ReadSubtree();
                                keywordReader.Read();
                                var keyword = (Keyword2Find)keywordSerializer.Deserialize(keywordReader);
                                keywordReader.Close();

                                allKeywords.Add(keyword);
                                reader.Read();
                            }
                            else
                            {
                                reader.Skip();
                            }
                        }

                        reader.ReadEndElement(); // Keywords
                    }
                    else
                    {
                        reader.Read();
                    }
                }
                else
                {
                    reader.Skip();
                }
            }

            reader.ReadEndElement();

            // Reconstruct folder structure from keywords' FolderPath
            ReconstructFolderStructure(allKeywords);
        }

        public void WriteXml(XmlWriter writer)
        {
            // Serialize all keywords with their folder paths
            writer.WriteStartElement("Keywords");

            var keywordSerializer = new XmlSerializer(typeof(Keyword2Find));
            var allKeywords = GetAllKeywords();

            foreach (var keyword in allKeywords)
            {
                keywordSerializer.Serialize(writer, keyword);
            }

            writer.WriteEndElement();
        }

        public IEnumerator GetEnumerator()
        {
            throw new NotImplementedException();
        }

        #endregion
    }
}