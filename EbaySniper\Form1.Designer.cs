﻿using System;
using System.ComponentModel;
using System.Windows.Forms;
using DevExpress.LookAndFeel;
using DevExpress.Office.UI;
using DevExpress.Utils;
using DevExpress.XtraBars;
using DevExpress.XtraBars.Docking;
using DevExpress.XtraBars.Docking2010;
using DevExpress.XtraBars.Docking2010.Views.Tabbed;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.BandedGrid;
using DevExpress.XtraLayout;
using DevExpress.XtraLayout.Converter;
using DevExpress.XtraRichEdit.Design;
using DevExpress.XtraRichEdit.Forms.Design;
using uBuyFirst.Data;
using uBuyFirst.Prefs;
using DevExpress.DataAccess.UI;
using uBuyFirst.Filters;

namespace uBuyFirst
{
    partial class Form1 : RibbonForm
    {

        /// <summary>
        /// Required designer variable.
        /// </summary>
        private IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                //autoreset.Dispose();
                for (int i = 0; i < components.Components.Count; i++)
                {
                    try
                    {
                        components.Components[i].Dispose();
                    }
                    catch (NullReferenceException ex)
                    {
                        if (ProgramState.Isdebug)
                            MessageBox.Show("Disposing: " + ex.Message);
                    }
                    catch(Exception ex)
                    {
                        if (ProgramState.Isdebug)
                            MessageBox.Show("Disposing #2: " + ex.Message);
                    }
                }
                //components.Dispose();
            }

            try
            {
                base.Dispose(disposing);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Form1));
            DevExpress.XtraEditors.Controls.EditorButtonImageOptions editorButtonImageOptions1 = new DevExpress.XtraEditors.Controls.EditorButtonImageOptions();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject1 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject2 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject3 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject4 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.XtraEditors.Controls.EditorButtonImageOptions editorButtonImageOptions2 = new DevExpress.XtraEditors.Controls.EditorButtonImageOptions();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject5 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject6 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject7 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject8 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SuperToolTip superToolTip1 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem1 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip2 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem2 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip3 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem3 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.Animation.FadeTransition fadeTransition1 = new DevExpress.Utils.Animation.FadeTransition();
            DevExpress.Utils.SuperToolTip superToolTip4 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem4 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip5 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem5 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip6 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipTitleItem toolTipTitleItem1 = new DevExpress.Utils.ToolTipTitleItem();
            DevExpress.Utils.ToolTipItem toolTipItem6 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip7 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipTitleItem toolTipTitleItem2 = new DevExpress.Utils.ToolTipTitleItem();
            DevExpress.Utils.ToolTipItem toolTipItem7 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip8 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipTitleItem toolTipTitleItem3 = new DevExpress.Utils.ToolTipTitleItem();
            DevExpress.Utils.SuperToolTip superToolTip9 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipTitleItem toolTipTitleItem4 = new DevExpress.Utils.ToolTipTitleItem();
            DevExpress.Utils.ToolTipItem toolTipItem8 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip10 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem9 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip11 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipTitleItem toolTipTitleItem5 = new DevExpress.Utils.ToolTipTitleItem();
            DevExpress.Utils.ToolTipItem toolTipItem10 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip12 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipTitleItem toolTipTitleItem6 = new DevExpress.Utils.ToolTipTitleItem();
            DevExpress.Utils.ToolTipItem toolTipItem11 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip13 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem12 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip14 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem13 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip15 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem14 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip16 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipTitleItem toolTipTitleItem7 = new DevExpress.Utils.ToolTipTitleItem();
            DevExpress.Utils.ToolTipItem toolTipItem15 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip17 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem16 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip18 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem17 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip19 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipTitleItem toolTipTitleItem8 = new DevExpress.Utils.ToolTipTitleItem();
            DevExpress.Utils.SuperToolTip superToolTip20 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem18 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip21 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipTitleItem toolTipTitleItem9 = new DevExpress.Utils.ToolTipTitleItem();
            DevExpress.Utils.ToolTipItem toolTipItem19 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip22 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem20 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip23 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem21 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.ContextButton contextButton1 = new DevExpress.Utils.ContextButton();
            DevExpress.XtraBars.Ribbon.GalleryItemGroup galleryItemGroup1 = new DevExpress.XtraBars.Ribbon.GalleryItemGroup();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule1 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleExpression formatConditionRuleExpression1 = new DevExpress.XtraEditors.FormatConditionRuleExpression();
            DevExpress.XtraEditors.Controls.EditorButtonImageOptions editorButtonImageOptions3 = new DevExpress.XtraEditors.Controls.EditorButtonImageOptions();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject9 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject10 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject11 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject12 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.XtraEditors.Controls.EditorButtonImageOptions editorButtonImageOptions4 = new DevExpress.XtraEditors.Controls.EditorButtonImageOptions();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject13 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject14 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject15 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject16 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SuperToolTip superToolTip24 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem22 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip25 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem23 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip26 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem24 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip27 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem25 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip28 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem26 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip29 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem27 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip30 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem28 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip31 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem29 = new DevExpress.Utils.ToolTipItem();
            DevExpress.XtraBars.Docking2010.Views.Tabbed.DockingContainer dockingContainer1 = new DevExpress.XtraBars.Docking2010.Views.Tabbed.DockingContainer();
            DevExpress.XtraBars.Docking2010.Views.Tabbed.DockingContainer dockingContainer2 = new DevExpress.XtraBars.Docking2010.Views.Tabbed.DockingContainer();
            DevExpress.XtraBars.Alerter.AlertButton alertButton1 = new DevExpress.XtraBars.Alerter.AlertButton();
            DevExpress.XtraBars.Alerter.AlertButton alertButton2 = new DevExpress.XtraBars.Alerter.AlertButton();
            DevExpress.XtraBars.Alerter.AlertButton alertButton3 = new DevExpress.XtraBars.Alerter.AlertButton();
            DevExpress.XtraBars.Alerter.AlertButton alertButton4 = new DevExpress.XtraBars.Alerter.AlertButton();
            this.cAlias = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.repositoryItemButtonNewKeyword = new DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit();
            this.documentGroup1 = new DevExpress.XtraBars.Docking2010.Views.Tabbed.DocumentGroup(this.components);
            this.document4 = new DevExpress.XtraBars.Docking2010.Views.Tabbed.Document(this.components);
            this.document6 = new DevExpress.XtraBars.Docking2010.Views.Tabbed.Document(this.components);
            this.document1 = new DevExpress.XtraBars.Docking2010.Views.Tabbed.Document(this.components);
            this.document5 = new DevExpress.XtraBars.Docking2010.Views.Tabbed.Document(this.components);
            this.document2 = new DevExpress.XtraBars.Docking2010.Views.Tabbed.Document(this.components);
            this.document3 = new DevExpress.XtraBars.Docking2010.Views.Tabbed.Document(this.components);
            this.document8 = new DevExpress.XtraBars.Docking2010.Views.Tabbed.Document(this.components);
            this.documentGroup2 = new DevExpress.XtraBars.Docking2010.Views.Tabbed.DocumentGroup(this.components);
            this.document7 = new DevExpress.XtraBars.Docking2010.Views.Tabbed.Document(this.components);
            this.document9 = new DevExpress.XtraBars.Docking2010.Views.Tabbed.Document(this.components);
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.BandedGrid.AdvBandedGridView();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.repositoryItemImageEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemImageEdit();
            this.repositoryItemButtonEditVisible = new DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit();
            this.repositoryItemButtonEditInvisible = new DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit();
            this.repositoryItemTextEditBrowser = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.toolTipController2 = new DevExpress.Utils.ToolTipController(this.components);
            this.cPriceMax = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.cSite = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.repositoryItemComboBoxSite = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.imageList16 = new System.Windows.Forms.ImageList(this.components);
            this.ribbonControl1 = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barButtonClear = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonSupport = new DevExpress.XtraBars.BarButtonItem();
            this.barStaticLicense = new DevExpress.XtraBars.BarStaticItem();
            this.barStaticErrorsVal = new DevExpress.XtraBars.BarStaticItem();
            this.barButtonStart = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonMakeOffer = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonBuy = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonSubscriptionInfo = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonTermsConditions = new DevExpress.XtraBars.BarButtonItem();
            this.barStaticBuildVersion = new DevExpress.XtraBars.BarStaticItem();
            this.menuDocking = new DevExpress.XtraBars.BarDockingMenuItem();
            this.barStaticProgress = new DevExpress.XtraBars.BarStaticItem();
            this.barWorkspaceMenuItem = new DevExpress.XtraBars.BarWorkspaceMenuItem();
            this.workspaceManager1 = new DevExpress.Utils.WorkspaceManager(this.components);
            this.barButtonResetWorkspace = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonCustomColumns = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemGridFont = new DevExpress.XtraBars.BarButtonItem();
            this.barEditItemRowHeight = new DevExpress.XtraBars.BarEditItem();
            this.repositoryItemSpinEditIdleTimeout = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.barEditItemAutoSelect = new DevExpress.XtraBars.BarEditItem();
            this.repositoryItemSpinEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.barButtonEbayAccounts = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemHightlightWords = new DevExpress.XtraBars.BarButtonItem();
            this.barCheckItemMaximizewindow = new DevExpress.XtraBars.BarCheckItem();
            this.barCheckItemSoundAlert = new DevExpress.XtraBars.BarCheckItem();
            this.barButtonItemSelectSound = new DevExpress.XtraBars.BarButtonItem();
            this.barEnableIdleTimeout = new DevExpress.XtraBars.BarCheckItem();
            this.barIdleTimeoutMinutes = new DevExpress.XtraBars.BarEditItem();
            this.barButtonItemTimeSync = new DevExpress.XtraBars.BarButtonItem();
            this.barStaticItemTimeDiffText = new DevExpress.XtraBars.BarStaticItem();
            this.barButtonItemDockPanels = new DevExpress.XtraBars.BarButtonItem();
            this.popupMenuDockPanels = new DevExpress.XtraBars.PopupMenu(this.components);
            this.barCheckItemNone = new DevExpress.XtraBars.BarCheckItem();
            this.barButtonItemCustomizeLayout = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemResetLayout = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemIncreaseFont = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemDecreaseFont = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemKeywords = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemFilters = new DevExpress.XtraBars.BarButtonItem();
            this.barCheckItemAutoStartSearch = new DevExpress.XtraBars.BarCheckItem();
            this.barEditItemTimeZone = new DevExpress.XtraBars.BarEditItem();
            this.repositoryItemComboBoxTimeZone = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.barCheckItemStartOnBoot = new DevExpress.XtraBars.BarCheckItem();
            this.barCheckItemPushBullet = new DevExpress.XtraBars.BarCheckItem();
            this.barButtonItemTrayAlert = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemCloseAll = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemTrayAlertOptions = new DevExpress.XtraBars.BarButtonItem();
            this.barEditItemHwid = new DevExpress.XtraBars.BarEditItem();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.barButtonItemPushBullet = new DevExpress.XtraBars.BarButtonItem();
            this.barStaticNotification = new DevExpress.XtraBars.BarStaticItem();
            this.barButtonItemMenuHelp = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemHelp = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonRestartOnUpdate = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemViews = new DevExpress.XtraBars.BarButtonItem();
            this.barCheckImmediatePayment = new DevExpress.XtraBars.BarCheckItem();
            this.barButtonItemShortcuts = new DevExpress.XtraBars.BarButtonItem();
            this.skinDropDownButtonItem1 = new DevExpress.XtraBars.SkinDropDownButtonItem();
            this.barButtonItemOpenInBrowser = new DevExpress.XtraBars.BarButtonItem();
            this.popupMenuOpenInBrowser = new DevExpress.XtraBars.PopupMenu(this.components);
            this.barCheckNoConfirmations = new DevExpress.XtraBars.BarCheckItem();
            this.barButtonItemTelegram = new DevExpress.XtraBars.BarButtonItem();
            this.barEditItemDescriptionColor = new DevExpress.XtraBars.BarEditItem();
            this.repositoryItemColorEditDescription = new DevExpress.XtraEditors.Repository.RepositoryItemColorEdit();
            this.barEditItemDescriptionZoom = new DevExpress.XtraBars.BarEditItem();
            this.repositoryItemSpinEditDescriptionZoom = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.barEditItemProfile = new DevExpress.XtraBars.BarEditItem();
            this.repositoryItemComboBoxProfile = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.barEditItemInitialResultsLimit = new DevExpress.XtraBars.BarEditItem();
            this.repositoryItemSpinEditInitialSearch = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.barButtonItemIgnoreSellers = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemRestoreBackup = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemCreateBackup = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemChangelog = new DevExpress.XtraBars.BarButtonItem();
            this.barCheckItemTotalPriceAsMaxPrice = new DevExpress.XtraBars.BarCheckItem();
            this.barCheckItemPriceOpensCheckout = new DevExpress.XtraBars.BarCheckItem();
            this.barButtonItemSync = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemGetExternalData = new DevExpress.XtraBars.BarButtonItem();
            this.barEditItemResultsMaxCount = new DevExpress.XtraBars.BarEditItem();
            this.repositoryItemResultsMaxCount = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.barCheckItemRestock = new DevExpress.XtraBars.BarCheckItem();
            this.barButtonItemRestockReport = new DevExpress.XtraBars.BarButtonItem();
            this.barStaticItemRestock = new DevExpress.XtraBars.BarStaticItem();
            this.barEditItemDailySpendLimit = new DevExpress.XtraBars.BarEditItem();
            this.repositoryItemSpinEditDailySpendLimit = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.barStaticItemTodaySpent = new DevExpress.XtraBars.BarStaticItem();
            this.ribbonPage1 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroupHome = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupKeywordsFilters = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupResults = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGrid = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroupItemDetails = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupAppearance = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupTopRow = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupResultsLimit = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupInitialResults = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupShortcuts = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupIgnoreSeller = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageDescriptionSettings = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroupHighLightWords = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupColor = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupDescriptionZoom = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageApplicationOptions = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroupEBayAccounts = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupNewItem = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupIdleTimeout = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupTimeSync = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupProgramLaunch = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageSync = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroupSync = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageData = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroupExternalData = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageView = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroupSkin = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupLayout = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageCheckout = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroupCheckout = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupProfile = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupRestock = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageHelp = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroupSupport = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup2 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup4 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.repositoryItemMemoExEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemMemoExEdit();
            this.repositoryItemColorEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemColorEdit();
            this.repositoryItemMemoEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit();
            this.repositoryItemMemoExEdit2 = new DevExpress.XtraEditors.Repository.RepositoryItemMemoExEdit();
            this.repositoryItemColorEdit2 = new DevExpress.XtraEditors.Repository.RepositoryItemColorEdit();
            this.repositoryItemOfficeColorEdit1 = new DevExpress.Office.UI.RepositoryItemOfficeColorEdit();
            this.repositoryItemColorPickEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemColorPickEdit();
            this.repositoryItemOfficeColorPickEdit1 = new DevExpress.Office.UI.RepositoryItemOfficeColorPickEdit();
            this.repositoryItemMemoEdit2 = new DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit();
            this.repositoryItemRichEditFontSizeEdit1 = new DevExpress.XtraRichEdit.Design.RepositoryItemRichEditFontSizeEdit();
            this.repositoryItemFontEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemFontEdit();
            this.repositoryItemFontStyle1 = new DevExpress.XtraRichEdit.Design.RepositoryItemFontStyle();
            this.repositoryItemBorderLineStyle1 = new DevExpress.XtraRichEdit.Forms.Design.RepositoryItemBorderLineStyle();
            this.repositoryItemCheckEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            this.ribbonStatusBar1 = new DevExpress.XtraBars.Ribbon.RibbonStatusBar();
            this.ribbonStatusBar2 = new DevExpress.XtraBars.Ribbon.RibbonStatusBar();
            this.btnCopyFilter = new DevExpress.XtraEditors.SimpleButton();
            this.btnEditXfilter = new DevExpress.XtraEditors.SimpleButton();
            this.btnAddXfilter = new DevExpress.XtraEditors.SimpleButton();
            this.lstchkXfilterList = new DevExpress.XtraEditors.CheckedListBoxControl();
            this.btnRemoveXfilter = new DevExpress.XtraEditors.SimpleButton();
            this.zoomTrackBarPictures = new DevExpress.XtraEditors.ZoomTrackBarControl();
            this.galleryControl1 = new DevExpress.XtraBars.Ribbon.GalleryControl();
            this.galleryControlClient1 = new DevExpress.XtraBars.Ribbon.GalleryControlClient();
            this.picBoxEbayLogo = new System.Windows.Forms.PictureBox();
            this.webBrowser1 = new System.Windows.Forms.WebBrowser();
            this.fontDialog1 = new System.Windows.Forms.FontDialog();
            this.openFileDialog1 = new System.Windows.Forms.OpenFileDialog();
            this.bgCheckoutWorker = new System.ComponentModel.BackgroundWorker();
            this.colorDialog1 = new System.Windows.Forms.ColorDialog();
            this.openFileDialog2 = new System.Windows.Forms.OpenFileDialog();
            this.notifyIcon1 = new System.Windows.Forms.NotifyIcon(this.components);
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemShow = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItemExit = new System.Windows.Forms.ToolStripMenuItem();
            this.fontDialogDGV1 = new System.Windows.Forms.FontDialog();
            this.dockPanel2_Container = new DevExpress.XtraBars.Docking.ControlContainer();
            this.dockPanel3_Container = new DevExpress.XtraBars.Docking.ControlContainer();
            this.dockPanel1_Container = new DevExpress.XtraBars.Docking.ControlContainer();
            this.formAssistant1 = new DevExpress.XtraBars.FormAssistant();
            this.dockFilters = new DevExpress.XtraBars.Docking.DockPanel();
            this.controlContainer1 = new DevExpress.XtraBars.Docking.ControlContainer();
            this.panelControl2 = new DevExpress.XtraEditors.PanelControl();
            this.btnSortFilters = new DevExpress.XtraEditors.SimpleButton();
            this.btnExportFilters = new DevExpress.XtraEditors.SimpleButton();
            this.btnImportFilters = new DevExpress.XtraEditors.SimpleButton();
            this.dockSearchQueries = new DevExpress.XtraBars.Docking.DockPanel();
            this.controlContainer2 = new DevExpress.XtraBars.Docking.ControlContainer();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.treeList1 = new DevExpress.XtraTreeList.TreeList();
            this.cEnabled = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.repositoryItemCheckEditEnabled = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            this.repositoryItemCheckEditFolder = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            this.repositoryItemCheckEditFolderOpen = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            this.cKeywords = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.cSearchInDescription = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.repositoryItemDescription = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            this.cPriceMin = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.cCategoryID = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.repositoryItemPopupContainerEditCategory = new DevExpress.XtraEditors.Repository.RepositoryItemPopupContainerEdit();
            this.popupContainerControlCategory = new DevExpress.XtraEditors.PopupContainerControl();
            this.treeListCategory = new DevExpress.XtraTreeList.TreeList();
            this.cCondition = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.repositoryItemCheckedComboBoxEditCondition = new DevExpress.XtraEditors.Repository.RepositoryItemCheckedComboBoxEdit();
            this.cLocatedIn = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.repositoryItemComboBoxLocatedIn = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.cShipsTo = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.repositoryItemComboBoxShipsTo = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.cZip = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.cSellers = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.cSellerType = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.repositoryItemComboBoxSellerType = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.cInterval = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.repositoryIntervalEdit = new DevExpress.XtraEditors.Repository.RepositoryItemTimeSpanEdit();
            this.cFilter = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.repositoryItemPopupContainerEditFilter = new DevExpress.XtraEditors.Repository.RepositoryItemPopupContainerEdit();
            this.popupContainerControl1 = new DevExpress.XtraEditors.PopupContainerControl();
            this.btnClearFilter = new DevExpress.XtraEditors.SimpleButton();
            this.btnSavePopupFilter = new DevExpress.XtraEditors.SimpleButton();
            this.filterControlTerm = new DevExpress.DataAccess.UI.FilterEditorControl();
            this.lblTermName = new DevExpress.XtraEditors.LabelControl();
            this.cResultWindow = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.repositoryItemViews = new DevExpress.XtraEditors.Repository.RepositoryItemMRUEdit();
            this.cListingType = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.repositoryItemCheckedComboBoxEditListingType = new DevExpress.XtraEditors.Repository.RepositoryItemCheckedComboBoxEdit();
            this.cPurchasedQuantity = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.cRequiredQuantity = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.cJobId = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.repositoryItemSpinEditThreads = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.repositoryItemEmpty4Filter = new DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit();
            this.repositoryItemEditTextData = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.repositoryItemTreeListLookUpEditCategory = new DevExpress.XtraEditors.Repository.RepositoryItemTreeListLookUpEdit();
            this.repositoryItemTreeListLookUpEdit1TreeList = new DevExpress.XtraTreeList.TreeList();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.checkUseAPI = new DevExpress.XtraEditors.CheckEdit();
            this.checkEditShowSoldItems = new DevExpress.XtraEditors.CheckEdit();
            this.checkUseRSS = new DevExpress.XtraEditors.CheckEdit();
            this.checkUseRSS2 = new DevExpress.XtraEditors.CheckEdit();
            this.lblFindReqMaxThreads = new DevExpress.XtraEditors.LabelControl();
            this.checkEditWhitespaceTokenizer = new DevExpress.XtraEditors.CheckEdit();
            this.spinEditFindReqMaxThreads = new DevExpress.XtraEditors.SpinEdit();
            this.btnDebugStatsInfo = new DevExpress.XtraEditors.SimpleButton();
            this.spinEditGetItemDetailsMaxThreads = new DevExpress.XtraEditors.SpinEdit();
            this.chkUpdateItemStatusFor2Min = new DevExpress.XtraEditors.CheckEdit();
            this.lblGetItemDetailsMaxThreads = new DevExpress.XtraEditors.LabelControl();
            this.chkDownloadAvatars = new DevExpress.XtraEditors.CheckEdit();
            this.chkDownloadOtherImages = new DevExpress.XtraEditors.CheckEdit();
            this.chkDownloadDescription = new DevExpress.XtraEditors.CheckEdit();
            this.panelControlDebugInfo = new DevExpress.XtraEditors.PanelControl();
            this.txtDebugInfo1 = new System.Windows.Forms.RichTextBox();
            this.panelControlKeywordsButtons = new DevExpress.XtraEditors.PanelControl();
            this.btnImportSearches = new DevExpress.XtraEditors.SimpleButton();
            this.btnCreateFolder = new DevExpress.XtraEditors.SimpleButton();
            this.btnNewSearchQuery = new DevExpress.XtraEditors.SimpleButton();
            this.btnExportSearches = new DevExpress.XtraEditors.SimpleButton();
            this.btnNewChildTerm = new DevExpress.XtraEditors.SimpleButton();
            this.btnRemoveSearch = new DevExpress.XtraEditors.SimpleButton();
            this.categoriesBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.dockItemProperties = new DevExpress.XtraBars.Docking.DockPanel();
            this.controlContainer5 = new DevExpress.XtraBars.Docking.ControlContainer();
            this.layoutControl1 = new DevExpress.XtraLayout.LayoutControl();
            this.lcShippingDays = new DevExpress.XtraEditors.LabelControl();
            this.lcBids = new DevExpress.XtraEditors.LabelControl();
            this.lcListingType = new DevExpress.XtraEditors.LabelControl();
            this.lcAuctionPrice = new DevExpress.XtraEditors.LabelControl();
            this.lcPayment = new DevExpress.XtraEditors.LabelControl();
            this.btnEditItemProperties = new DevExpress.XtraEditors.SimpleButton();
            this.lcEbayAccount = new DevExpress.XtraEditors.ComboBoxEdit();
            this.lcItemID = new DevExpress.XtraEditors.LabelControl();
            this.lcTerm = new DevExpress.XtraEditors.LabelControl();
            this.lcTitle = new DevExpress.XtraEditors.LabelControl();
            this.lcCondition = new DevExpress.XtraEditors.LabelControl();
            this.lcReturns = new DevExpress.XtraEditors.LabelControl();
            this.lcBestOffer = new DevExpress.XtraEditors.LabelControl();
            this.lcFoundTime = new DevExpress.XtraEditors.LabelControl();
            this.lcLocation = new DevExpress.XtraEditors.LabelControl();
            this.lcFromCountry = new DevExpress.XtraEditors.LabelControl();
            this.lcToCountry = new DevExpress.XtraEditors.LabelControl();
            this.lcAutoPay = new DevExpress.XtraEditors.LabelControl();
            this.lcCategoryID = new DevExpress.XtraEditors.LabelControl();
            this.lcCategoryName = new DevExpress.XtraEditors.LabelControl();
            this.lcConditionDescription = new DevExpress.XtraEditors.LabelControl();
            this.lcFeedbackRating = new DevExpress.XtraEditors.LabelControl();
            this.lcFeedbackScore = new DevExpress.XtraEditors.LabelControl();
            this.lcItemPrice = new DevExpress.XtraEditors.LabelControl();
            this.lcPostedTime = new DevExpress.XtraEditors.LabelControl();
            this.lcQuantity = new DevExpress.XtraEditors.LabelControl();
            this.lcSellerName = new DevExpress.XtraEditors.LabelControl();
            this.lcShipping = new DevExpress.XtraEditors.LabelControl();
            this.lcShippingType = new DevExpress.XtraEditors.LabelControl();
            this.lcShipAdditionalItem = new DevExpress.XtraEditors.LabelControl();
            this.lcSoldTime = new DevExpress.XtraEditors.LabelControl();
            this.lcEbayWebsite = new DevExpress.XtraEditors.LabelControl();
            this.lcPageViews = new DevExpress.XtraEditors.LabelControl();
            this.lcUPC = new DevExpress.XtraEditors.LabelControl();
            this.lcVariation = new DevExpress.XtraEditors.LabelControl();
            this.lcTotalPrice = new DevExpress.XtraEditors.LabelControl();
            this.lciVariation = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciUPC = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciTerm = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciSoldTime = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciShipAdditionalItem = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciShippingType = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciPageViews = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciPostedTime = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciFoundTime = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciEbayWebsite = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciEbayAccount = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciCategoryName = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciCategoryID = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciBestOffer = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciAutoPay = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciItemPrice = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciShipping = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciToCountry = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciFromCountry = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciPayment = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciShippingDays = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.lciTotalPrice = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciConditionDescription = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciCondition = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciReturns = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciTitle = new DevExpress.XtraLayout.LayoutControlItem();
            this.simpleSeparator1 = new DevExpress.XtraLayout.SimpleSeparator();
            this.lciSellerName = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciLocation = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciItemID = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciFeedbackScore = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciFeedbackRating = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciQuantity = new DevExpress.XtraLayout.LayoutControlItem();
            this.simpleSeparator2 = new DevExpress.XtraLayout.SimpleSeparator();
            this.simpleSeparator3 = new DevExpress.XtraLayout.SimpleSeparator();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem2 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.lciAuctionPrice = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciListingType = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciBids = new DevExpress.XtraLayout.LayoutControlItem();
            this.lcipanelPicturesControl = new DevExpress.XtraLayout.LayoutControlItem();
            this.panelPicturesControl = new DevExpress.XtraEditors.PanelControl();
            this.layoutControlForPicture = new DevExpress.XtraLayout.LayoutControl();
            this.panelPicturesSettingControl = new DevExpress.XtraEditors.PanelControl();
            this.zoomTrackBarExpanded = new DevExpress.XtraEditors.ZoomTrackBarControl();
            this.svgLargeImageBox = new DevExpress.XtraEditors.SvgImageBox();
            this.svgSmallImageBox = new DevExpress.XtraEditors.SvgImageBox();
            this.btnEditPictureProperties = new DevExpress.XtraEditors.SimpleButton();
            this.layoutControlGroupPictures = new DevExpress.XtraLayout.LayoutControlGroup();
            this.lcipanelPicturesOpenSettingsControl = new DevExpress.XtraLayout.LayoutControlItem();
            this.lcipanelPicturesSettingControl = new DevExpress.XtraLayout.LayoutControlItem();
            this.pictureSettingsButton2 = new DevExpress.XtraEditors.SimpleButton();
            this.dockManager1 = new DevExpress.XtraBars.Docking.DockManager(this.components);
            this.dockDescription = new DevExpress.XtraBars.Docking.DockPanel();
            this.dockPanel5_Container = new DevExpress.XtraBars.Docking.ControlContainer();
            this.splitContainerControlBrowser = new DevExpress.XtraEditors.SplitContainerControl();
            this.linkeBayPrivacyPolicy = new DevExpress.XtraEditors.HyperlinkLabelControl();
            this.linkeBayUserAgreement = new DevExpress.XtraEditors.HyperlinkLabelControl();
            this.linkReportItem = new DevExpress.XtraEditors.HyperlinkLabelControl();
            this.zoomTrackBarBrowser = new DevExpress.XtraEditors.ZoomTrackBarControl();
            this.colorPickBrowser = new DevExpress.XtraEditors.ColorPickEdit();
            this.btnHighlightWords = new DevExpress.XtraEditors.SimpleButton();
            this.flyoutPanelBrowser = new DevExpress.Utils.FlyoutPanel();
            this.flyoutPanelControl1 = new DevExpress.Utils.FlyoutPanelControl();
            this.btnBrowserSettings = new DevExpress.XtraEditors.SimpleButton();
            this.dockLog = new DevExpress.XtraBars.Docking.DockPanel();
            this.dockPanel7_Container = new DevExpress.XtraBars.Docking.ControlContainer();
            this.xtraTabLog = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPageFilterLog = new DevExpress.XtraTab.XtraTabPage();
            this.panelControlFilterLog = new DevExpress.XtraEditors.PanelControl();
            this.btnClearFilterLog = new DevExpress.XtraEditors.SimpleButton();
            this.memoEditFilterLog = new DevExpress.XtraEditors.MemoEdit();
            this.xtraTabPageErrorLog = new DevExpress.XtraTab.XtraTabPage();
            this.memoEditErrorLog = new DevExpress.XtraEditors.MemoEdit();
            this.panelControlErrorLog = new DevExpress.XtraEditors.PanelControl();
            this.hyperlinkLabelOpoenLogFolder = new DevExpress.XtraEditors.HyperlinkLabelControl();
            this.btnClearLogs = new DevExpress.XtraEditors.SimpleButton();
            this.dockPictures = new DevExpress.XtraBars.Docking.DockPanel();
            this.controlContainer6 = new DevExpress.XtraBars.Docking.ControlContainer();
            this.dockPanelBuy = new DevExpress.XtraBars.Docking.DockPanel();
            this.controlContainer4 = new DevExpress.XtraBars.Docking.ControlContainer();
            this.splitContainerBuyOffer = new DevExpress.XtraEditors.SplitContainerControl();
            this.panelBuyButton = new DevExpress.XtraEditors.SimpleButton();
            this.btnMakeOffer = new DevExpress.XtraEditors.SimpleButton();
            this.dockPanelExternalData = new DevExpress.XtraBars.Docking.DockPanel();
            this.controlContainer3 = new DevExpress.XtraBars.Docking.ControlContainer();
            this.layoutControlCefBrowser = new DevExpress.XtraLayout.LayoutControl();
            this.panelCefBrowser = new DevExpress.XtraEditors.PanelControl();
            this.layoutControlGroupExternalData = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem2 = new DevExpress.XtraLayout.LayoutControlItem();
            this.dockPanelWatchlist = new DevExpress.XtraBars.Docking.DockPanel();
            this.controlContainer7 = new DevExpress.XtraBars.Docking.ControlContainer();
            this.layoutWatchlistControls = new DevExpress.XtraLayout.LayoutControl();
            this.lnkWatchlistImportFromClipboard = new DevExpress.XtraEditors.HyperlinkLabelControl();
            this.chkRefreshAndNotifyWatchlist = new DevExpress.XtraEditors.ToggleSwitch();
            this.btnRefreshWatchlist = new DevExpress.XtraEditors.SimpleButton();
            this.timeSpanWatchlistRefreshInterval = new DevExpress.XtraEditors.TimeSpanEdit();
            this.layoutWatchlistRoot = new DevExpress.XtraLayout.LayoutControlGroup();
            this.lciWatchlist = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem3 = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciWatchlistRefreshInterval = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem4 = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem3 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.splitterItem1 = new DevExpress.XtraLayout.SplitterItem();
            this.splitterItem2 = new DevExpress.XtraLayout.SplitterItem();
            this.layoutControlItem5 = new DevExpress.XtraLayout.LayoutControlItem();
            this.dockMainGrid = new DevExpress.XtraBars.Docking.DockPanel();
            this.dockPanel6_Container = new DevExpress.XtraBars.Docking.ControlContainer();
            this.ribbonControl2 = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.barButtonItem1 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem2 = new DevExpress.XtraBars.BarButtonItem();
            this.barStaticItem1 = new DevExpress.XtraBars.BarStaticItem();
            this.barStaticItem3 = new DevExpress.XtraBars.BarStaticItem();
            this.barButtonItem3 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem4 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem5 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem6 = new DevExpress.XtraBars.BarButtonItem();
            this.barStaticItem4 = new DevExpress.XtraBars.BarStaticItem();
            this.barStaticItem5 = new DevExpress.XtraBars.BarStaticItem();
            this.barDockingMenuItem1 = new DevExpress.XtraBars.BarDockingMenuItem();
            this.barHeaderItem3 = new DevExpress.XtraBars.BarHeaderItem();
            this.barHeaderItem4 = new DevExpress.XtraBars.BarHeaderItem();
            this.barStaticItem6 = new DevExpress.XtraBars.BarStaticItem();
            this.barButtonItem7 = new DevExpress.XtraBars.BarButtonItem();
            this.ribbonPage2 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup6 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPage3 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup7 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup8 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPage5 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup9 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup10 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonStatusBar3 = new DevExpress.XtraBars.Ribbon.RibbonStatusBar();
            this.ribbonControl3 = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.barButtonItem8 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem9 = new DevExpress.XtraBars.BarButtonItem();
            this.barStaticItem7 = new DevExpress.XtraBars.BarStaticItem();
            this.barButtonItem10 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem11 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem12 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem13 = new DevExpress.XtraBars.BarButtonItem();
            this.barStaticItem10 = new DevExpress.XtraBars.BarStaticItem();
            this.barStaticItem11 = new DevExpress.XtraBars.BarStaticItem();
            this.barDockingMenuItem2 = new DevExpress.XtraBars.BarDockingMenuItem();
            this.barHeaderItem5 = new DevExpress.XtraBars.BarHeaderItem();
            this.barHeaderItem6 = new DevExpress.XtraBars.BarHeaderItem();
            this.barStaticItem12 = new DevExpress.XtraBars.BarStaticItem();
            this.barButtonItem14 = new DevExpress.XtraBars.BarButtonItem();
            this.ribbonPage6 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup11 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPage7 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup12 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup13 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPage8 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup14 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup15 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonStatusBar4 = new DevExpress.XtraBars.Ribbon.RibbonStatusBar();
            this.documentManager1 = new DevExpress.XtraBars.Docking2010.DocumentManager(this.components);
            this.tabbedView1 = new DevExpress.XtraBars.Docking2010.Views.Tabbed.TabbedView(this.components);
            this.ribbonPageGroup18 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup19 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.defaultLookAndFeel1 = new DevExpress.LookAndFeel.DefaultLookAndFeel(this.components);
            this.popupMenuItemDetails = new DevExpress.XtraBars.PopupMenu(this.components);
            this.comboBoxEdit1 = new DevExpress.XtraEditors.ComboBoxEdit();
            this.layoutConverter1 = new DevExpress.XtraLayout.Converter.LayoutConverter(this.components);
            this.alertControl1 = new DevExpress.XtraBars.Alerter.AlertControl(this.components);
            this.popupMenuTrayAlert = new DevExpress.XtraBars.PopupMenu(this.components);
            this.behaviorManager1 = new DevExpress.Utils.Behaviors.BehaviorManager(this.components);
            this.ribbonPageGroup1 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPage9 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPage10 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemButtonNewKeyword)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.documentGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.document4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.document6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.document1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.document5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.document2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.document3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.document8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.documentGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.document7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.document9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemImageEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemButtonEditVisible)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemButtonEditInvisible)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEditBrowser)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBoxSite)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEditIdleTimeout)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenuDockPanels)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBoxTimeZone)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenuOpenInBrowser)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemColorEditDescription)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEditDescriptionZoom)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBoxProfile)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEditInitialSearch)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemResultsMaxCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEditDailySpendLimit)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoExEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemColorEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoExEdit2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemColorEdit2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemOfficeColorEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemColorPickEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemOfficeColorPickEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemRichEditFontSizeEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemFontEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemFontStyle1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemBorderLineStyle1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lstchkXfilterList)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.zoomTrackBarPictures)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.zoomTrackBarPictures.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.galleryControl1)).BeginInit();
            this.galleryControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.picBoxEbayLogo)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.dockFilters.SuspendLayout();
            this.controlContainer1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).BeginInit();
            this.panelControl2.SuspendLayout();
            this.dockSearchQueries.SuspendLayout();
            this.controlContainer2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2.Panel1)).BeginInit();
            this.splitContainerControl2.Panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2.Panel2)).BeginInit();
            this.splitContainerControl2.Panel2.SuspendLayout();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeList1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEditEnabled)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEditFolder)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEditFolderOpen)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemDescription)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemPopupContainerEditCategory)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupContainerControlCategory)).BeginInit();
            this.popupContainerControlCategory.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListCategory)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckedComboBoxEditCondition)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBoxLocatedIn)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBoxShipsTo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBoxSellerType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryIntervalEdit)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemPopupContainerEditFilter)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupContainerControl1)).BeginInit();
            this.popupContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemViews)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckedComboBoxEditListingType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEditThreads)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemEmpty4Filter)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemEditTextData)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTreeListLookUpEditCategory)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTreeListLookUpEdit1TreeList)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkUseAPI.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditShowSoldItems.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkUseRSS.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkUseRSS2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditWhitespaceTokenizer.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditFindReqMaxThreads.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditGetItemDetailsMaxThreads.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkUpdateItemStatusFor2Min.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkDownloadAvatars.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkDownloadOtherImages.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkDownloadDescription.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControlDebugInfo)).BeginInit();
            this.panelControlDebugInfo.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelControlKeywordsButtons)).BeginInit();
            this.panelControlKeywordsButtons.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.categoriesBindingSource)).BeginInit();
            this.dockItemProperties.SuspendLayout();
            this.controlContainer5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).BeginInit();
            this.layoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lcEbayAccount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciVariation)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciUPC)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciTerm)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciSoldTime)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShipAdditionalItem)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShippingType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciPageViews)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciPostedTime)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciFoundTime)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciEbayWebsite)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciEbayAccount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciCategoryName)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciCategoryID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciBestOffer)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciAutoPay)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciItemPrice)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShipping)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciToCountry)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciFromCountry)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciPayment)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShippingDays)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciTotalPrice)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciConditionDescription)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciCondition)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciReturns)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciTitle)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.simpleSeparator1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciSellerName)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciLocation)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciItemID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciFeedbackScore)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciFeedbackRating)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciQuantity)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.simpleSeparator2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.simpleSeparator3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciAuctionPrice)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciListingType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciBids)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lcipanelPicturesControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelPicturesControl)).BeginInit();
            this.panelPicturesControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlForPicture)).BeginInit();
            this.layoutControlForPicture.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelPicturesSettingControl)).BeginInit();
            this.panelPicturesSettingControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.zoomTrackBarExpanded)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.zoomTrackBarExpanded.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.svgLargeImageBox)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.svgSmallImageBox)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroupPictures)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lcipanelPicturesOpenSettingsControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lcipanelPicturesSettingControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dockManager1)).BeginInit();
            this.dockDescription.SuspendLayout();
            this.dockPanel5_Container.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControlBrowser)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControlBrowser.Panel1)).BeginInit();
            this.splitContainerControlBrowser.Panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControlBrowser.Panel2)).BeginInit();
            this.splitContainerControlBrowser.Panel2.SuspendLayout();
            this.splitContainerControlBrowser.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.zoomTrackBarBrowser)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.zoomTrackBarBrowser.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorPickBrowser.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.flyoutPanelBrowser)).BeginInit();
            this.flyoutPanelBrowser.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.flyoutPanelControl1)).BeginInit();
            this.dockLog.SuspendLayout();
            this.dockPanel7_Container.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabLog)).BeginInit();
            this.xtraTabLog.SuspendLayout();
            this.xtraTabPageFilterLog.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelControlFilterLog)).BeginInit();
            this.panelControlFilterLog.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.memoEditFilterLog.Properties)).BeginInit();
            this.xtraTabPageErrorLog.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.memoEditErrorLog.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControlErrorLog)).BeginInit();
            this.panelControlErrorLog.SuspendLayout();
            this.dockPictures.SuspendLayout();
            this.controlContainer6.SuspendLayout();
            this.dockPanelBuy.SuspendLayout();
            this.controlContainer4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerBuyOffer)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerBuyOffer.Panel1)).BeginInit();
            this.splitContainerBuyOffer.Panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerBuyOffer.Panel2)).BeginInit();
            this.splitContainerBuyOffer.Panel2.SuspendLayout();
            this.splitContainerBuyOffer.SuspendLayout();
            this.dockPanelExternalData.SuspendLayout();
            this.controlContainer3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlCefBrowser)).BeginInit();
            this.layoutControlCefBrowser.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelCefBrowser)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroupExternalData)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).BeginInit();
            this.dockPanelWatchlist.SuspendLayout();
            this.controlContainer7.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.layoutWatchlistControls)).BeginInit();
            this.layoutWatchlistControls.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkRefreshAndNotifyWatchlist.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeSpanWatchlistRefreshInterval.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutWatchlistRoot)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciWatchlist)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciWatchlistRefreshInterval)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitterItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitterItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem5)).BeginInit();
            this.dockMainGrid.SuspendLayout();
            this.dockPanel6_Container.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.documentManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabbedView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenuItemDetails)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenuTrayAlert)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.behaviorManager1)).BeginInit();
            this.SuspendLayout();
            //
            // cAlias
            //
            this.cAlias.Caption = "Alias";
            this.cAlias.ColumnEdit = this.repositoryItemButtonNewKeyword;
            this.cAlias.FieldName = "Alias";
            this.cAlias.MinWidth = 34;
            this.cAlias.Name = "cAlias";
            this.cAlias.ToolTip = resources.GetString("cAlias.ToolTip");
            this.cAlias.Visible = true;
            this.cAlias.VisibleIndex = 1;
            this.cAlias.Width = 103;
            //
            // repositoryItemButtonNewKeyword
            //
            this.repositoryItemButtonNewKeyword.AutoHeight = false;
            this.repositoryItemButtonNewKeyword.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Plus, "Add Sub Search", -1, true, true, false, editorButtonImageOptions1, new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject1, serializableAppearanceObject2, serializableAppearanceObject3, serializableAppearanceObject4, "Add Sub Search (Ctrl+Insert)", null, null, DevExpress.Utils.ToolTipAnchor.Default)});
            this.repositoryItemButtonNewKeyword.Name = "repositoryItemButtonNewKeyword";
            this.repositoryItemButtonNewKeyword.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.repositoryItemButtonEditAlias_ButtonClick);
            //
            // documentGroup1
            //
            this.documentGroup1.Items.AddRange(new DevExpress.XtraBars.Docking2010.Views.Tabbed.Document[] {
            this.document4,
            this.document6,
            this.document1,
            this.document5,
            this.document2,
            this.document3,
            this.document8});
            //
            // document4
            //
            this.document4.Caption = "eBay Searches";
            this.document4.ControlName = "dockSearchQueries";
            this.document4.ControlTypeName = null;
            this.document4.FloatLocation = new System.Drawing.Point(821, 305);
            this.document4.FloatSize = new System.Drawing.Size(758, 216);
            this.document4.Properties.AllowClose = DevExpress.Utils.DefaultBoolean.True;
            this.document4.Properties.AllowFloat = DevExpress.Utils.DefaultBoolean.True;
            this.document4.Properties.AllowFloatOnDoubleClick = DevExpress.Utils.DefaultBoolean.True;
            //
            // document6
            //
            this.document6.Caption = "Filters";
            this.document6.ControlName = "dockFilters";
            this.document6.ControlTypeName = null;
            this.document6.FloatLocation = new System.Drawing.Point(175, 251);
            this.document6.FloatSize = new System.Drawing.Size(758, 216);
            this.document6.Properties.AllowClose = DevExpress.Utils.DefaultBoolean.True;
            this.document6.Properties.AllowFloat = DevExpress.Utils.DefaultBoolean.True;
            this.document6.Properties.AllowFloatOnDoubleClick = DevExpress.Utils.DefaultBoolean.True;
            //
            // document1
            //
            this.document1.Caption = "Description";
            this.document1.ControlName = "dockDescription";
            this.document1.ControlTypeName = null;
            this.document1.FloatLocation = new System.Drawing.Point(1190, 363);
            this.document1.FloatSize = new System.Drawing.Size(758, 216);
            this.document1.Properties.AllowClose = DevExpress.Utils.DefaultBoolean.True;
            this.document1.Properties.AllowFloat = DevExpress.Utils.DefaultBoolean.True;
            this.document1.Properties.AllowFloatOnDoubleClick = DevExpress.Utils.DefaultBoolean.True;
            //
            // document5
            //
            this.document5.Caption = "Item Properties";
            this.document5.ControlName = "dockItemProperties";
            this.document5.ControlTypeName = null;
            this.document5.FloatLocation = new System.Drawing.Point(906, 422);
            this.document5.FloatSize = new System.Drawing.Size(758, 216);
            this.document5.Properties.AllowClose = DevExpress.Utils.DefaultBoolean.True;
            this.document5.Properties.AllowFloat = DevExpress.Utils.DefaultBoolean.True;
            this.document5.Properties.AllowFloatOnDoubleClick = DevExpress.Utils.DefaultBoolean.True;
            //
            // document2
            //
            this.document2.Caption = "Log";
            this.document2.ControlName = "dockLog";
            this.document2.ControlTypeName = null;
            this.document2.FloatLocation = new System.Drawing.Point(675, 354);
            this.document2.FloatSize = new System.Drawing.Size(1556, 77);
            this.document2.Properties.AllowClose = DevExpress.Utils.DefaultBoolean.True;
            this.document2.Properties.AllowFloat = DevExpress.Utils.DefaultBoolean.True;
            this.document2.Properties.AllowFloatOnDoubleClick = DevExpress.Utils.DefaultBoolean.True;
            //
            // document3
            //
            this.document3.Caption = "";
            this.document3.ControlName = "dockPanelBuy";
            this.document3.ControlTypeName = null;
            this.document3.FloatLocation = new System.Drawing.Point(681, 576);
            this.document3.FloatSize = new System.Drawing.Size(65, 77);
            this.document3.Properties.AllowClose = DevExpress.Utils.DefaultBoolean.True;
            this.document3.Properties.AllowFloat = DevExpress.Utils.DefaultBoolean.True;
            this.document3.Properties.AllowFloatOnDoubleClick = DevExpress.Utils.DefaultBoolean.True;
            //
            // document8
            //
            this.document8.Caption = "External Data";
            this.document8.ControlName = "dockPanelExternalData";
            this.document8.ControlTypeName = null;
            this.document8.FloatLocation = new System.Drawing.Point(655, 364);
            this.document8.FloatSize = new System.Drawing.Size(200, 200);
            this.document8.Properties.AllowClose = DevExpress.Utils.DefaultBoolean.True;
            this.document8.Properties.AllowFloat = DevExpress.Utils.DefaultBoolean.True;
            this.document8.Properties.AllowFloatOnDoubleClick = DevExpress.Utils.DefaultBoolean.True;
            //
            // documentGroup2
            //
            this.documentGroup2.Items.AddRange(new DevExpress.XtraBars.Docking2010.Views.Tabbed.Document[] {
            this.document7,
            this.document9});
            //
            // document7
            //
            this.document7.Caption = "Pictures";
            this.document7.ControlName = "dockPictures";
            this.document7.ControlTypeName = null;
            this.document7.FloatLocation = new System.Drawing.Point(533, 592);
            this.document7.FloatSize = new System.Drawing.Size(758, 216);
            this.document7.Properties.AllowClose = DevExpress.Utils.DefaultBoolean.True;
            this.document7.Properties.AllowFloat = DevExpress.Utils.DefaultBoolean.True;
            this.document7.Properties.AllowFloatOnDoubleClick = DevExpress.Utils.DefaultBoolean.True;
            //
            // document9
            //
            this.document9.Caption = "Watchlist";
            this.document9.ControlName = "dockPanelWatchlist";
            this.document9.ControlTypeName = null;
            this.document9.FloatLocation = new System.Drawing.Point(1611, 606);
            this.document9.FloatSize = new System.Drawing.Size(200, 200);
            this.document9.Properties.AllowClose = DevExpress.Utils.DefaultBoolean.True;
            this.document9.Properties.AllowFloat = DevExpress.Utils.DefaultBoolean.True;
            this.document9.Properties.AllowFloatOnDoubleClick = DevExpress.Utils.DefaultBoolean.True;
            //
            // gridControl1
            //
            this.gridControl1.AllowRestoreSelectionAndFocusedRow = DevExpress.Utils.DefaultBoolean.True;
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.Location = new System.Drawing.Point(0, 0);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemImageEdit1,
            this.repositoryItemButtonEditVisible,
            this.repositoryItemButtonEditInvisible,
            this.repositoryItemTextEditBrowser});
            this.gridControl1.Size = new System.Drawing.Size(585, 223);
            this.gridControl1.TabIndex = 0;
            this.gridControl1.ToolTipController = this.toolTipController2;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            //
            // gridView1
            //
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.gridView1.Appearance.Row.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView1.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand1,
            this.gridBand2,
            this.gridBand3});
            this.gridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "ItemID", null, "     [{0}]", 1)});
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.True;
            this.gridView1.OptionsBehavior.AllowIncrementalSearch = true;
            this.gridView1.OptionsBehavior.ImmediateUpdateRowPosition = false;
            this.gridView1.OptionsBehavior.KeepFocusedRowOnUpdate = false;
            this.gridView1.OptionsBehavior.KeepGroupExpandedOnSorting = false;
            this.gridView1.OptionsCustomization.AllowChangeBandParent = true;
            this.gridView1.OptionsCustomization.AllowChangeColumnParent = true;
            this.gridView1.OptionsCustomization.CustomizationFormSearchBoxVisible = true;
            this.gridView1.OptionsFilter.UseNewCustomFilterDialog = true;
            this.gridView1.OptionsMenu.ShowAddNewSummaryItem = DevExpress.Utils.DefaultBoolean.False;
            this.gridView1.OptionsMenu.ShowGroupSummaryEditorItem = true;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView1.OptionsSelection.EnableAppearanceHideSelection = false;
            this.gridView1.OptionsSelection.MultiSelect = true;
            this.gridView1.OptionsView.AllowGlyphSkinning = true;
            this.gridView1.OptionsView.GroupDrawMode = DevExpress.XtraGrid.Views.Grid.GroupDrawMode.Office;
            this.gridView1.OptionsView.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowAlways;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.KeyDown += new System.Windows.Forms.KeyEventHandler(this.Dgv1KeyDown);
            //
            // gridBand1
            //
            this.gridBand1.Caption = "General";
            this.gridBand1.Name = "gridBand1";
            this.gridBand1.OptionsBand.AllowMove = false;
            this.gridBand1.OptionsBand.AllowPress = false;
            this.gridBand1.OptionsBand.ShowCaption = false;
            this.gridBand1.VisibleIndex = 0;
            this.gridBand1.Width = 75;
            //
            // gridBand2
            //
            this.gridBand2.Caption = "Other";
            this.gridBand2.Name = "gridBand2";
            this.gridBand2.OptionsBand.ShowCaption = false;
            this.gridBand2.VisibleIndex = 1;
            //
            // gridBand3
            //
            this.gridBand3.Caption = "Custom";
            this.gridBand3.Name = "gridBand3";
            this.gridBand3.OptionsBand.ShowCaption = false;
            this.gridBand3.VisibleIndex = 2;
            //
            // repositoryItemImageEdit1
            //
            this.repositoryItemImageEdit1.AutoHeight = false;
            this.repositoryItemImageEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemImageEdit1.Name = "repositoryItemImageEdit1";
            //
            // repositoryItemButtonEditVisible
            //
            this.repositoryItemButtonEditVisible.AutoHeight = false;
            this.repositoryItemButtonEditVisible.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph)});
            this.repositoryItemButtonEditVisible.Name = "repositoryItemButtonEditVisible";
            //
            // repositoryItemButtonEditInvisible
            //
            this.repositoryItemButtonEditInvisible.AutoHeight = false;
            this.repositoryItemButtonEditInvisible.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Left, "", -1, true, false, false, editorButtonImageOptions2, new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject5, serializableAppearanceObject6, serializableAppearanceObject7, serializableAppearanceObject8, "", null, null, DevExpress.Utils.ToolTipAnchor.Default)});
            this.repositoryItemButtonEditInvisible.Name = "repositoryItemButtonEditInvisible";
            //
            // repositoryItemTextEditBrowser
            //
            this.repositoryItemTextEditBrowser.AutoHeight = false;
            this.repositoryItemTextEditBrowser.ContextImageOptions.Alignment = DevExpress.XtraEditors.ContextImageAlignment.Far;
            this.repositoryItemTextEditBrowser.ContextImageOptions.Image = global::uBuyFirst.Properties.Resources.Cart16;
            this.repositoryItemTextEditBrowser.Name = "repositoryItemTextEditBrowser";
            this.repositoryItemTextEditBrowser.ReadOnly = true;
            //
            // toolTipController2
            //
            this.toolTipController2.InitialDelay = 100;
            this.toolTipController2.Rounded = true;
            this.toolTipController2.RoundRadius = 1;
            this.toolTipController2.ShowShadow = false;
            this.toolTipController2.GetActiveObjectInfo += new DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventHandler(this.toolTipController2_GetActiveObjectInfo);
            //
            // cPriceMax
            //
            this.cPriceMax.Caption = "Price Max";
            this.cPriceMax.FieldName = "Price Max";
            this.cPriceMax.Name = "cPriceMax";
            this.cPriceMax.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Decimal;
            this.cPriceMax.Visible = true;
            this.cPriceMax.VisibleIndex = 5;
            this.cPriceMax.Width = 57;
            //
            // cSite
            //
            this.cSite.Caption = "Site";
            this.cSite.ColumnEdit = this.repositoryItemComboBoxSite;
            this.cSite.FieldName = "Site";
            this.cSite.FilterMode = DevExpress.XtraGrid.ColumnFilterMode.DisplayText;
            this.cSite.Name = "cSite";
            this.cSite.ToolTip = "The eBay country website to search for items. ";
            this.cSite.Visible = true;
            this.cSite.VisibleIndex = 8;
            this.cSite.Width = 55;
            //
            // repositoryItemComboBoxSite
            //
            this.repositoryItemComboBoxSite.AutoHeight = false;
            this.repositoryItemComboBoxSite.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemComboBoxSite.Name = "repositoryItemComboBoxSite";
            this.repositoryItemComboBoxSite.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            //
            // imageList16
            //
            this.imageList16.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList16.ImageStream")));
            this.imageList16.TransparentColor = System.Drawing.Color.Transparent;
            this.imageList16.Images.SetKeyName(0, "Fatcow-Farm-Fresh-Filter.ico");
            this.imageList16.Images.SetKeyName(1, "incorrect-294245_640.png");
            this.imageList16.Images.SetKeyName(2, "plus-31216_640.png");
            this.imageList16.Images.SetKeyName(3, "Edit-256.png");
            this.imageList16.Images.SetKeyName(4, "Message.png");
            this.imageList16.Images.SetKeyName(5, "1421299050_plus.png");
            this.imageList16.Images.SetKeyName(6, "1421299095_multiply.png");
            this.imageList16.Images.SetKeyName(7, "1421300434_arrow-up-01-128.png");
            this.imageList16.Images.SetKeyName(8, "1421300430_arrow-down-01-128.png");
            this.imageList16.Images.SetKeyName(9, "1421299043_engineering.png");
            this.imageList16.Images.SetKeyName(10, "1421298798_empty_filter.png");
            this.imageList16.Images.SetKeyName(11, "1423063495_ic_content_copy_48px-16.png");
            this.imageList16.Images.SetKeyName(12, "export-32.png");
            this.imageList16.Images.SetKeyName(13, "import-32.png");
            //
            // ribbonControl1
            //
            this.ribbonControl1.AllowCustomization = true;
            this.ribbonControl1.AllowDrop = true;
            this.ribbonControl1.AllowGlyphSkinning = true;
            this.ribbonControl1.AllowKeyTips = false;
            this.ribbonControl1.ApplicationButtonImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("ribbonControl1.ApplicationButtonImageOptions.Image")));
            this.ribbonControl1.Controller = this.barAndDockingController1;
            this.ribbonControl1.ExpandCollapseItem.Id = 0;
            this.ribbonControl1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbonControl1.ExpandCollapseItem,
            this.barButtonClear,
            this.barButtonSupport,
            this.barStaticLicense,
            this.barStaticErrorsVal,
            this.barButtonStart,
            this.barButtonMakeOffer,
            this.barButtonBuy,
            this.barButtonSubscriptionInfo,
            this.barButtonTermsConditions,
            this.barStaticBuildVersion,
            this.menuDocking,
            this.barStaticProgress,
            this.barWorkspaceMenuItem,
            this.barButtonResetWorkspace,
            this.barButtonCustomColumns,
            this.barButtonItemGridFont,
            this.barEditItemRowHeight,
            this.barEditItemAutoSelect,
            this.barButtonEbayAccounts,
            this.barButtonItemHightlightWords,
            this.barCheckItemMaximizewindow,
            this.barCheckItemSoundAlert,
            this.barButtonItemSelectSound,
            this.barEnableIdleTimeout,
            this.barIdleTimeoutMinutes,
            this.barButtonItemTimeSync,
            this.barStaticItemTimeDiffText,
            this.barButtonItemDockPanels,
            this.barButtonItemCustomizeLayout,
            this.barButtonItemResetLayout,
            this.barButtonItemIncreaseFont,
            this.barButtonItemDecreaseFont,
            this.barCheckItemNone,
            this.barButtonItemKeywords,
            this.barButtonItemFilters,
            this.barCheckItemAutoStartSearch,
            this.barEditItemTimeZone,
            this.barCheckItemStartOnBoot,
            this.barCheckItemPushBullet,
            this.barButtonItemTrayAlert,
            this.barButtonItemCloseAll,
            this.barButtonItemTrayAlertOptions,
            this.barEditItemHwid,
            this.barButtonItemPushBullet,
            this.barStaticNotification,
            this.barButtonItemMenuHelp,
            this.barButtonItemHelp,
            this.barButtonRestartOnUpdate,
            this.barButtonItemViews,
            this.barCheckImmediatePayment,
            this.barButtonItemShortcuts,
            this.skinDropDownButtonItem1,
            this.barButtonItemOpenInBrowser,
            this.barCheckNoConfirmations,
            this.barButtonItemTelegram,
            this.barEditItemDescriptionColor,
            this.barEditItemDescriptionZoom,
            this.barEditItemProfile,
            this.barEditItemInitialResultsLimit,
            this.barButtonItemIgnoreSellers,
            this.barButtonItemRestoreBackup,
            this.barButtonItemCreateBackup,
            this.barButtonItemChangelog,
            this.barCheckItemTotalPriceAsMaxPrice,
            this.barCheckItemPriceOpensCheckout,
            this.barButtonItemSync,
            this.barButtonItemGetExternalData,
            this.barEditItemResultsMaxCount,
            this.barCheckItemRestock,
            this.barButtonItemRestockReport,
            this.barStaticItemRestock,
            this.barEditItemDailySpendLimit,
            this.barStaticItemTodaySpent});
            this.ribbonControl1.Location = new System.Drawing.Point(0, 0);
            this.ribbonControl1.Margin = new System.Windows.Forms.Padding(2);
            this.ribbonControl1.MaxItemId = 26;
            this.ribbonControl1.Name = "ribbonControl1";
            this.ribbonControl1.OptionsCustomizationForm.AllowBarSubItems = true;
            this.ribbonControl1.OptionsCustomizationForm.AllowEditBarItemPopups = true;
            this.ribbonControl1.OptionsCustomizationForm.AllowLinkCustomization = true;
            this.ribbonControl1.OptionsMenuMinWidth = 264;
            this.ribbonControl1.Pages.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPage[] {
            this.ribbonPage1,
            this.ribbonPageGrid,
            this.ribbonPageDescriptionSettings,
            this.ribbonPageApplicationOptions,
            this.ribbonPageSync,
            this.ribbonPageData,
            this.ribbonPageView,
            this.ribbonPageCheckout,
            this.ribbonPageHelp});
            this.ribbonControl1.QuickToolbarItemLinks.Add(this.barButtonStart);
            this.ribbonControl1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemMemoExEdit1,
            this.repositoryItemColorEdit1,
            this.repositoryItemMemoEdit1,
            this.repositoryItemMemoExEdit2,
            this.repositoryItemColorEdit2,
            this.repositoryItemOfficeColorEdit1,
            this.repositoryItemColorPickEdit1,
            this.repositoryItemOfficeColorPickEdit1,
            this.repositoryItemMemoEdit2,
            this.repositoryItemSpinEditIdleTimeout,
            this.repositoryItemRichEditFontSizeEdit1,
            this.repositoryItemFontEdit1,
            this.repositoryItemFontStyle1,
            this.repositoryItemSpinEdit1,
            this.repositoryItemBorderLineStyle1,
            this.repositoryItemComboBoxTimeZone,
            this.repositoryItemTextEdit1,
            this.repositoryItemColorEditDescription,
            this.repositoryItemSpinEditDescriptionZoom,
            this.repositoryItemComboBoxProfile,
            this.repositoryItemSpinEditInitialSearch,
            this.repositoryItemCheckEdit1,
            this.repositoryItemResultsMaxCount,
            this.repositoryItemSpinEditDailySpendLimit});
            this.ribbonControl1.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonControlStyle.Office2013;
            this.ribbonControl1.ShowApplicationButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowExpandCollapseButton = DevExpress.Utils.DefaultBoolean.True;
            this.ribbonControl1.ShowPageKeyTipsMode = DevExpress.XtraBars.Ribbon.ShowPageKeyTipsMode.Show;
            this.ribbonControl1.Size = new System.Drawing.Size(1078, 158);
            this.ribbonControl1.StatusBar = this.ribbonStatusBar2;
            //
            // barAndDockingController1
            //
            this.barAndDockingController1.AppearancesDocking.PanelCaptionActive.Options.UseFont = true;
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesDocking.ViewStyle = DevExpress.XtraBars.Docking2010.Views.DockingViewStyle.Classic;
            //
            // barButtonClear
            //
            this.barButtonClear.Caption = "Clear results";
            this.barButtonClear.Id = 31;
            this.barButtonClear.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Clear;
            this.barButtonClear.Name = "barButtonClear";
            toolTipItem1.Text = "Clear all listing data in all grids.\r\nDouble click to clear a list of already fou" +
    "nd items. Useful when testing searches.";
            superToolTip1.Items.Add(toolTipItem1);
            this.barButtonClear.SuperTip = superToolTip1;
            this.barButtonClear.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonClear_ItemClick);
            this.barButtonClear.ItemDoubleClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonClear_ItemDoubleClick);
            //
            // barButtonSupport
            //
            this.barButtonSupport.Caption = "Support";
            this.barButtonSupport.Id = 224;
            this.barButtonSupport.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Support;
            this.barButtonSupport.Name = "barButtonSupport";
            toolTipItem2.Text = "Report a bug, issue or request/vote on new features.";
            superToolTip2.Items.Add(toolTipItem2);
            this.barButtonSupport.SuperTip = superToolTip2;
            this.barButtonSupport.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonReport_ItemClick);
            //
            // barStaticLicense
            //
            this.barStaticLicense.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barStaticLicense.Caption = "License: Verifying...";
            this.barStaticLicense.Id = 2312;
            this.barStaticLicense.ItemAppearance.Hovered.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Underline);
            this.barStaticLicense.ItemAppearance.Hovered.ForeColor = System.Drawing.SystemColors.HotTrack;
            this.barStaticLicense.ItemAppearance.Hovered.Options.UseFont = true;
            this.barStaticLicense.ItemAppearance.Hovered.Options.UseForeColor = true;
            this.barStaticLicense.Name = "barStaticLicense";
            this.barStaticLicense.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barStaticLicense_ItemClick);
            //
            // barStaticErrorsVal
            //
            this.barStaticErrorsVal.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barStaticErrorsVal.Caption = "Errors: 0";
            this.barStaticErrorsVal.Id = 551;
            this.barStaticErrorsVal.Name = "barStaticErrorsVal";
            this.barStaticErrorsVal.ItemDoubleClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barStaticErrorsVal_ItemDoubleClick);
            //
            // barButtonStart
            //
            this.barButtonStart.Caption = "Loading...";
            this.barButtonStart.Enabled = false;
            this.barButtonStart.Id = 261;
            this.barButtonStart.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Start;
            this.barButtonStart.Name = "barButtonStart";
            this.barButtonStart.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonStart_ItemClick);
            //
            // barButtonMakeOffer
            //
            this.barButtonMakeOffer.Caption = "Make Offer";
            this.barButtonMakeOffer.Id = 44;
            this.barButtonMakeOffer.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.MakeOffer;
            this.barButtonMakeOffer.Name = "barButtonMakeOffer";
            this.barButtonMakeOffer.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonMakeOffer_ItemClick);
            //
            // barButtonBuy
            //
            this.barButtonBuy.Caption = "Buy";
            this.barButtonBuy.Id = 271;
            this.barButtonBuy.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Buy1;
            this.barButtonBuy.Name = "barButtonBuy";
            this.barButtonBuy.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonBuy_ItemClick);
            //
            // barButtonSubscriptionInfo
            //
            this.barButtonSubscriptionInfo.Caption = "Subscription Info";
            this.barButtonSubscriptionInfo.Id = 104;
            this.barButtonSubscriptionInfo.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Subscription;
            this.barButtonSubscriptionInfo.Name = "barButtonSubscriptionInfo";
            toolTipItem3.Text = "Signup, view or change your subscription plan.";
            superToolTip3.Items.Add(toolTipItem3);
            this.barButtonSubscriptionInfo.SuperTip = superToolTip3;
            this.barButtonSubscriptionInfo.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonSubscriptionInfo_ItemClick);
            //
            // barButtonTermsConditions
            //
            this.barButtonTermsConditions.Caption = "Terms and Conditions";
            this.barButtonTermsConditions.Id = 225;
            this.barButtonTermsConditions.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Terms;
            this.barButtonTermsConditions.Name = "barButtonTermsConditions";
            this.barButtonTermsConditions.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem3_ItemClick);
            //
            // barStaticBuildVersion
            //
            this.barStaticBuildVersion.Caption = "Build: v.********";
            this.barStaticBuildVersion.Id = 23312;
            this.barStaticBuildVersion.Name = "barStaticBuildVersion";
            this.barStaticBuildVersion.ItemDoubleClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barStaticBuildVersion_ItemClick);
            //
            // menuDocking
            //
            this.menuDocking.Caption = "Docking";
            this.menuDocking.Id = 251;
            this.menuDocking.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Dock;
            this.menuDocking.Name = "menuDocking";
            //
            // barStaticProgress
            //
            this.barStaticProgress.AutoSize = DevExpress.XtraBars.BarStaticItemSize.None;
            this.barStaticProgress.Id = 105;
            this.barStaticProgress.Name = "barStaticProgress";
            this.barStaticProgress.Width = 30;
            //
            // barWorkspaceMenuItem
            //
            this.barWorkspaceMenuItem.Caption = "Workspace";
            this.barWorkspaceMenuItem.Id = 226;
            this.barWorkspaceMenuItem.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Workspaces;
            this.barWorkspaceMenuItem.Name = "barWorkspaceMenuItem";
            this.barWorkspaceMenuItem.WorkspaceManager = this.workspaceManager1;
            //
            // workspaceManager1
            //
            this.workspaceManager1.CloseStreamOnWorkspaceLoading = DevExpress.Utils.DefaultBoolean.True;
            this.workspaceManager1.CloseStreamOnWorkspaceSaving = DevExpress.Utils.DefaultBoolean.True;
            this.workspaceManager1.TargetControl = this;
            this.workspaceManager1.TransitionType = fadeTransition1;
            this.workspaceManager1.AfterApplyWorkspace += new System.EventHandler(this.workspaceManager1_AfterApplyWorkspace);
            this.workspaceManager1.WorkspaceSaved += new DevExpress.Utils.WorkspaceEventHandler(this.workspaceManager1_WorkspaceSaved);
            //
            // barButtonResetWorkspace
            //
            this.barButtonResetWorkspace.Caption = "Reset Layout";
            this.barButtonResetWorkspace.Id = 106;
            this.barButtonResetWorkspace.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.ResetLayout;
            this.barButtonResetWorkspace.Name = "barButtonResetWorkspace";
            toolTipItem4.Text = "Panels and grid columns will be set to a default state.\r\nApplication will be clos" +
    "ed. \r\nPlease, start the it again.";
            superToolTip4.Items.Add(toolTipItem4);
            this.barButtonResetWorkspace.SuperTip = superToolTip4;
            this.barButtonResetWorkspace.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemResetWorkspace_ItemClick);
            //
            // barButtonCustomColumns
            //
            this.barButtonCustomColumns.Caption = "Items Specifics";
            this.barButtonCustomColumns.Id = 227;
            this.barButtonCustomColumns.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.CustomColumns;
            this.barButtonCustomColumns.Name = "barButtonCustomColumns";
            this.barButtonCustomColumns.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonCustomColumns_ItemClick);
            //
            // barButtonItemGridFont
            //
            this.barButtonItemGridFont.Caption = "Grid font";
            this.barButtonItemGridFont.Id = 14;
            this.barButtonItemGridFont.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Font;
            this.barButtonItemGridFont.Name = "barButtonItemGridFont";
            toolTipItem5.Text = "Choose font properties for results grid.";
            superToolTip5.Items.Add(toolTipItem5);
            this.barButtonItemGridFont.SuperTip = superToolTip5;
            this.barButtonItemGridFont.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemGridFont_ItemClick);
            //
            // barEditItemRowHeight
            //
            this.barEditItemRowHeight.Caption = "Row height";
            this.barEditItemRowHeight.Edit = this.repositoryItemSpinEditIdleTimeout;
            this.barEditItemRowHeight.EditValue = new decimal(new int[] {
            22,
            0,
            0,
            0});
            this.barEditItemRowHeight.Id = 15;
            this.barEditItemRowHeight.Name = "barEditItemRowHeight";
            toolTipTitleItem1.Text = "Rows height in results grid.\r\n";
            toolTipItem6.LeftIndent = 6;
            superToolTip6.Items.Add(toolTipTitleItem1);
            superToolTip6.Items.Add(toolTipItem6);
            this.barEditItemRowHeight.SuperTip = superToolTip6;
            this.barEditItemRowHeight.EditValueChanged += new System.EventHandler(this.barEditItemRowHeight_EditValueChanged);
            //
            // repositoryItemSpinEditIdleTimeout
            //
            this.repositoryItemSpinEditIdleTimeout.AutoHeight = false;
            this.repositoryItemSpinEditIdleTimeout.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemSpinEditIdleTimeout.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.repositoryItemSpinEditIdleTimeout.IsFloatValue = false;
            this.repositoryItemSpinEditIdleTimeout.MaskSettings.Set("mask", "D");
            this.repositoryItemSpinEditIdleTimeout.MaxValue = new decimal(new int[] {
            500,
            0,
            0,
            0});
            this.repositoryItemSpinEditIdleTimeout.MinValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.repositoryItemSpinEditIdleTimeout.Name = "repositoryItemSpinEditIdleTimeout";
            //
            // barEditItemAutoSelect
            //
            this.barEditItemAutoSelect.Caption = "Auto select";
            this.barEditItemAutoSelect.Edit = this.repositoryItemSpinEdit1;
            this.barEditItemAutoSelect.EditValue = new decimal(new int[] {
            30,
            0,
            0,
            0});
            this.barEditItemAutoSelect.Id = 219;
            this.barEditItemAutoSelect.Name = "barEditItemAutoSelect";
            toolTipTitleItem2.Text = "Auto select new listing time";
            toolTipItem7.LeftIndent = 6;
            superToolTip7.Items.Add(toolTipTitleItem2);
            superToolTip7.Items.Add(toolTipItem7);
            this.barEditItemAutoSelect.SuperTip = superToolTip7;
            this.barEditItemAutoSelect.EditValueChanged += new System.EventHandler(this.barEditItemAutoSelect_EditValueChanged);
            //
            // repositoryItemSpinEdit1
            //
            this.repositoryItemSpinEdit1.AutoHeight = false;
            this.repositoryItemSpinEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemSpinEdit1.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.repositoryItemSpinEdit1.IsFloatValue = false;
            this.repositoryItemSpinEdit1.MaskSettings.Set("mask", "D");
            this.repositoryItemSpinEdit1.MaxValue = new decimal(new int[] {
            99999,
            0,
            0,
            0});
            this.repositoryItemSpinEdit1.Name = "repositoryItemSpinEdit1";
            //
            // barButtonEbayAccounts
            //
            this.barButtonEbayAccounts.Caption = "eBay Accounts";
            this.barButtonEbayAccounts.Id = 2012;
            this.barButtonEbayAccounts.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Accounts;
            this.barButtonEbayAccounts.Name = "barButtonEbayAccounts";
            this.barButtonEbayAccounts.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonEbayAccounts_ItemClick);
            //
            // barButtonItemHightlightWords
            //
            this.barButtonItemHightlightWords.Caption = "Highlight Words";
            this.barButtonItemHightlightWords.Hint = "Highlight words in item Description";
            this.barButtonItemHightlightWords.Id = 21;
            this.barButtonItemHightlightWords.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Highlight;
            this.barButtonItemHightlightWords.Name = "barButtonItemHightlightWords";
            this.barButtonItemHightlightWords.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemHighlightWords_ItemClick);
            //
            // barCheckItemMaximizewindow
            //
            this.barCheckItemMaximizewindow.Caption = "Maximize window";
            this.barCheckItemMaximizewindow.CheckBoxVisibility = DevExpress.XtraBars.CheckBoxVisibility.BeforeText;
            this.barCheckItemMaximizewindow.Id = 24;
            this.barCheckItemMaximizewindow.Name = "barCheckItemMaximizewindow";
            toolTipTitleItem3.Text = "Popup and maximize program window when new item is found.";
            superToolTip8.Items.Add(toolTipTitleItem3);
            this.barCheckItemMaximizewindow.SuperTip = superToolTip8;
            //
            // barCheckItemSoundAlert
            //
            this.barCheckItemSoundAlert.Caption = "Sound alert";
            this.barCheckItemSoundAlert.CheckBoxVisibility = DevExpress.XtraBars.CheckBoxVisibility.BeforeText;
            this.barCheckItemSoundAlert.Id = 107;
            this.barCheckItemSoundAlert.Name = "barCheckItemSoundAlert";
            toolTipTitleItem4.Text = "Sound alert";
            toolTipItem8.LeftIndent = 6;
            toolTipItem8.Text = "Sound will be played when new item is found.\r\nDefault sound is Windows raindrop s" +
    "ound file.\r\n";
            superToolTip9.Items.Add(toolTipTitleItem4);
            superToolTip9.Items.Add(toolTipItem8);
            this.barCheckItemSoundAlert.SuperTip = superToolTip9;
            //
            // barButtonItemSelectSound
            //
            this.barButtonItemSelectSound.Caption = "Select sound";
            this.barButtonItemSelectSound.Id = 2341;
            this.barButtonItemSelectSound.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItemSelectSound.ImageOptions.Image")));
            this.barButtonItemSelectSound.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItemSelectSound.ImageOptions.LargeImage")));
            this.barButtonItemSelectSound.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.OpenFile;
            this.barButtonItemSelectSound.Name = "barButtonItemSelectSound";
            this.barButtonItemSelectSound.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.SmallWithText;
            toolTipItem9.Text = "Browser local PC for custom sound file to be played when new listing is found.";
            superToolTip10.Items.Add(toolTipItem9);
            this.barButtonItemSelectSound.SuperTip = superToolTip10;
            this.barButtonItemSelectSound.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemSelectSound_ItemClick);
            //
            // barEnableIdleTimeout
            //
            this.barEnableIdleTimeout.BindableChecked = true;
            this.barEnableIdleTimeout.Caption = "Idle timeout";
            this.barEnableIdleTimeout.CheckBoxVisibility = DevExpress.XtraBars.CheckBoxVisibility.BeforeText;
            this.barEnableIdleTimeout.Checked = true;
            this.barEnableIdleTimeout.Id = 85;
            this.barEnableIdleTimeout.Name = "barEnableIdleTimeout";
            toolTipTitleItem5.Text = "Auto pause searches after X minutes";
            toolTipItem10.LeftIndent = 6;
            superToolTip11.Items.Add(toolTipTitleItem5);
            superToolTip11.Items.Add(toolTipItem10);
            this.barEnableIdleTimeout.SuperTip = superToolTip11;
            this.barEnableIdleTimeout.CheckedChanged += new DevExpress.XtraBars.ItemClickEventHandler(this.barCheckItemEnableIdleTimeout_CheckedChanged);
            //
            // barIdleTimeoutMinutes
            //
            this.barIdleTimeoutMinutes.Caption = "Idle timeout";
            this.barIdleTimeoutMinutes.Edit = this.repositoryItemSpinEditIdleTimeout;
            this.barIdleTimeoutMinutes.EditValue = new decimal(new int[] {
            30,
            0,
            0,
            0});
            this.barIdleTimeoutMinutes.Id = 291;
            this.barIdleTimeoutMinutes.Name = "barIdleTimeoutMinutes";
            toolTipTitleItem6.Text = "Auto pause searches after X minutes";
            toolTipItem11.LeftIndent = 6;
            superToolTip12.Items.Add(toolTipTitleItem6);
            superToolTip12.Items.Add(toolTipItem11);
            this.barIdleTimeoutMinutes.SuperTip = superToolTip12;
            //
            // barButtonItemTimeSync
            //
            this.barButtonItemTimeSync.Caption = "Sync now";
            this.barButtonItemTimeSync.Id = 310;
            this.barButtonItemTimeSync.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Sync;
            this.barButtonItemTimeSync.Name = "barButtonItemTimeSync";
            this.barButtonItemTimeSync.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.SmallWithText;
            toolTipItem12.Text = "Computer administrator privileges are required for time synchronization";
            superToolTip13.Items.Add(toolTipItem12);
            this.barButtonItemTimeSync.SuperTip = superToolTip13;
            this.barButtonItemTimeSync.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemTimeSync_ItemClick);
            //
            // barStaticItemTimeDiffText
            //
            this.barStaticItemTimeDiffText.Caption = "eBay - Local time: 0.0";
            this.barStaticItemTimeDiffText.Id = 411;
            this.barStaticItemTimeDiffText.Name = "barStaticItemTimeDiffText";
            toolTipItem13.Text = "Time difference between local PC and eBay servers.\r\n\r\nComputer administrator priv" +
    "ileges are required for time synchronization";
            superToolTip14.Items.Add(toolTipItem13);
            this.barStaticItemTimeDiffText.SuperTip = superToolTip14;
            //
            // barButtonItemDockPanels
            //
            this.barButtonItemDockPanels.ActAsDropDown = true;
            this.barButtonItemDockPanels.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
            this.barButtonItemDockPanels.Caption = "Panels";
            this.barButtonItemDockPanels.DropDownControl = this.popupMenuDockPanels;
            this.barButtonItemDockPanels.Id = 371;
            this.barButtonItemDockPanels.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Panels;
            this.barButtonItemDockPanels.Name = "barButtonItemDockPanels";
            this.barButtonItemDockPanels.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemDockPanels_ItemClick);
            //
            // popupMenuDockPanels
            //
            this.popupMenuDockPanels.ItemLinks.Add(this.barCheckItemNone);
            this.popupMenuDockPanels.Name = "popupMenuDockPanels";
            this.popupMenuDockPanels.Ribbon = this.ribbonControl1;
            //
            // barCheckItemNone
            //
            this.barCheckItemNone.Id = 108;
            this.barCheckItemNone.Name = "barCheckItemNone";
            //
            // barButtonItemCustomizeLayout
            //
            this.barButtonItemCustomizeLayout.Caption = "Customize Layout";
            this.barButtonItemCustomizeLayout.Id = 293;
            this.barButtonItemCustomizeLayout.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItemCustomizeLayout.ImageOptions.Image")));
            this.barButtonItemCustomizeLayout.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItemCustomizeLayout.ImageOptions.LargeImage")));
            this.barButtonItemCustomizeLayout.Name = "barButtonItemCustomizeLayout";
            this.barButtonItemCustomizeLayout.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemCustomizeLayout_ItemClick);
            //
            // barButtonItemResetLayout
            //
            this.barButtonItemResetLayout.Caption = "Reset Layout";
            this.barButtonItemResetLayout.Id = 410;
            this.barButtonItemResetLayout.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItemResetLayout.ImageOptions.Image")));
            this.barButtonItemResetLayout.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItemResetLayout.ImageOptions.LargeImage")));
            this.barButtonItemResetLayout.Name = "barButtonItemResetLayout";
            this.barButtonItemResetLayout.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemResetLayout_ItemClick);
            //
            // barButtonItemIncreaseFont
            //
            this.barButtonItemIncreaseFont.Caption = "Increase Font Size";
            this.barButtonItemIncreaseFont.Id = 511;
            this.barButtonItemIncreaseFont.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItemIncreaseFont.ImageOptions.Image")));
            this.barButtonItemIncreaseFont.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItemIncreaseFont.ImageOptions.LargeImage")));
            this.barButtonItemIncreaseFont.Name = "barButtonItemIncreaseFont";
            this.barButtonItemIncreaseFont.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemIncreaseFont_ItemClick);
            //
            // barButtonItemDecreaseFont
            //
            this.barButtonItemDecreaseFont.Caption = "Decrease Font Size";
            this.barButtonItemDecreaseFont.Id = 412;
            this.barButtonItemDecreaseFont.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItemDecreaseFont.ImageOptions.Image")));
            this.barButtonItemDecreaseFont.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItemDecreaseFont.ImageOptions.LargeImage")));
            this.barButtonItemDecreaseFont.Name = "barButtonItemDecreaseFont";
            this.barButtonItemDecreaseFont.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemDecreaseFont_ItemClick);
            //
            // barButtonItemKeywords
            //
            this.barButtonItemKeywords.Caption = "Keywords";
            this.barButtonItemKeywords.Id = 5532;
            this.barButtonItemKeywords.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("barButtonItemKeywords.ImageOptions.SvgImage")));
            this.barButtonItemKeywords.Name = "barButtonItemKeywords";
            this.barButtonItemKeywords.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemKeywords_ItemClick);
            //
            // barButtonItemFilters
            //
            this.barButtonItemFilters.Caption = "Filters";
            this.barButtonItemFilters.Id = 2351;
            this.barButtonItemFilters.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Filter;
            this.barButtonItemFilters.Name = "barButtonItemFilters";
            this.barButtonItemFilters.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemFilters_ItemClick);
            //
            // barCheckItemAutoStartSearch
            //
            this.barCheckItemAutoStartSearch.Caption = "Auto start search";
            this.barCheckItemAutoStartSearch.CheckBoxVisibility = DevExpress.XtraBars.CheckBoxVisibility.BeforeText;
            this.barCheckItemAutoStartSearch.Id = 3109;
            this.barCheckItemAutoStartSearch.Name = "barCheckItemAutoStartSearch";
            toolTipItem14.Text = "Auto click \"Start\" button when program launched";
            superToolTip15.Items.Add(toolTipItem14);
            this.barCheckItemAutoStartSearch.SuperTip = superToolTip15;
            //
            // barEditItemTimeZone
            //
            this.barEditItemTimeZone.Caption = "Time Zone";
            this.barEditItemTimeZone.Edit = this.repositoryItemComboBoxTimeZone;
            this.barEditItemTimeZone.Id = 2361;
            this.barEditItemTimeZone.Name = "barEditItemTimeZone";
            toolTipTitleItem7.Text = "Use Time Zone to display Time";
            toolTipItem15.LeftIndent = 6;
            toolTipItem15.Text = "Current Time Zone";
            superToolTip16.Items.Add(toolTipTitleItem7);
            superToolTip16.Items.Add(toolTipItem15);
            this.barEditItemTimeZone.SuperTip = superToolTip16;
            this.barEditItemTimeZone.EditValueChanged += new System.EventHandler(this.barEditItemTimeZone_EditValueChanged);
            this.barEditItemTimeZone.ShownEditor += new DevExpress.XtraBars.ItemClickEventHandler(this.barEditItemTimeZone_ShownEditor);
            //
            // repositoryItemComboBoxTimeZone
            //
            this.repositoryItemComboBoxTimeZone.AutoHeight = false;
            this.repositoryItemComboBoxTimeZone.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemComboBoxTimeZone.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Custom;
            this.repositoryItemComboBoxTimeZone.Name = "repositoryItemComboBoxTimeZone";
            this.repositoryItemComboBoxTimeZone.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            //
            // barCheckItemStartOnBoot
            //
            this.barCheckItemStartOnBoot.Caption = "Start on boot";
            this.barCheckItemStartOnBoot.CheckBoxVisibility = DevExpress.XtraBars.CheckBoxVisibility.BeforeText;
            this.barCheckItemStartOnBoot.Id = 109;
            this.barCheckItemStartOnBoot.Name = "barCheckItemStartOnBoot";
            toolTipItem16.Text = "Launch uBuyFirst when computer starts";
            superToolTip17.Items.Add(toolTipItem16);
            this.barCheckItemStartOnBoot.SuperTip = superToolTip17;
            this.barCheckItemStartOnBoot.CheckedChanged += new DevExpress.XtraBars.ItemClickEventHandler(this.barCheckItemStartOnBoot_CheckedChanged);
            //
            // barCheckItemPushBullet
            //
            this.barCheckItemPushBullet.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barCheckItemPushBullet.Caption = "Pushbullet";
            this.barCheckItemPushBullet.CheckBoxVisibility = DevExpress.XtraBars.CheckBoxVisibility.BeforeText;
            this.barCheckItemPushBullet.Id = 252;
            this.barCheckItemPushBullet.Name = "barCheckItemPushBullet";
            this.barCheckItemPushBullet.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barCheckItemPushBullet.CheckedChanged += new DevExpress.XtraBars.ItemClickEventHandler(this.barCheckItemPushBullet_CheckedChanged);
            //
            // barButtonItemTrayAlert
            //
            this.barButtonItemTrayAlert.Caption = "Tray Alert";
            this.barButtonItemTrayAlert.Id = 110;
            this.barButtonItemTrayAlert.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItemTrayAlert.ImageOptions.Image")));
            this.barButtonItemTrayAlert.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItemTrayAlert.ImageOptions.LargeImage")));
            this.barButtonItemTrayAlert.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Alert;
            this.barButtonItemTrayAlert.Name = "barButtonItemTrayAlert";
            this.barButtonItemTrayAlert.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.SmallWithText;
            this.barButtonItemTrayAlert.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemTrayAlert_ItemClick);
            //
            // barButtonItemCloseAll
            //
            this.barButtonItemCloseAll.Caption = "Close all";
            this.barButtonItemCloseAll.Id = 7732;
            this.barButtonItemCloseAll.Name = "barButtonItemCloseAll";
            this.barButtonItemCloseAll.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemCloseAll_ItemClick);
            //
            // barButtonItemTrayAlertOptions
            //
            this.barButtonItemTrayAlertOptions.Caption = "Options";
            this.barButtonItemTrayAlertOptions.Id = 232;
            this.barButtonItemTrayAlertOptions.Name = "barButtonItemTrayAlertOptions";
            this.barButtonItemTrayAlertOptions.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemTrayAlertOptions_ItemClick);
            //
            // barEditItemHwid
            //
            this.barEditItemHwid.Caption = "HWID";
            this.barEditItemHwid.Edit = this.repositoryItemTextEdit1;
            this.barEditItemHwid.Id = 42;
            this.barEditItemHwid.Name = "barEditItemHwid";
            this.barEditItemHwid.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            //
            // repositoryItemTextEdit1
            //
            this.repositoryItemTextEdit1.AutoHeight = false;
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            //
            // barButtonItemPushBullet
            //
            this.barButtonItemPushBullet.Caption = "PushBullet";
            this.barButtonItemPushBullet.Id = 111;
            this.barButtonItemPushBullet.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Pushbullet;
            this.barButtonItemPushBullet.Name = "barButtonItemPushBullet";
            this.barButtonItemPushBullet.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.SmallWithText;
            toolTipItem17.Text = "Push notifications with PushBullet for Android and Web";
            superToolTip18.Items.Add(toolTipItem17);
            this.barButtonItemPushBullet.SuperTip = superToolTip18;
            this.barButtonItemPushBullet.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemPushBullet_ItemClick);
            //
            // barStaticNotification
            //
            this.barStaticNotification.Id = 52;
            this.barStaticNotification.Name = "barStaticNotification";
            this.barStaticNotification.TextAlignment = System.Drawing.StringAlignment.Far;
            //
            // barButtonItemMenuHelp
            //
            this.barButtonItemMenuHelp.Caption = "Help";
            this.barButtonItemMenuHelp.Id = 112;
            this.barButtonItemMenuHelp.Name = "barButtonItemMenuHelp";
            this.barButtonItemMenuHelp.Tag = "https://ubuyfirst.com/07-item-properties-panel/";
            this.barButtonItemMenuHelp.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.OpenTagLinkByButton);
            //
            // barButtonItemHelp
            //
            this.barButtonItemHelp.Caption = "Help";
            this.barButtonItemHelp.Id = 234;
            this.barButtonItemHelp.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Help;
            this.barButtonItemHelp.Name = "barButtonItemHelp";
            this.barButtonItemHelp.Tag = "https://ubuyfirst.com/docs/";
            this.barButtonItemHelp.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.OpenTagLinkByButton);
            //
            // barButtonRestartOnUpdate
            //
            this.barButtonRestartOnUpdate.Caption = "Update now";
            this.barButtonRestartOnUpdate.Hint = "Program update is ready to install.";
            this.barButtonRestartOnUpdate.Id = 143;
            this.barButtonRestartOnUpdate.ItemAppearance.Normal.BackColor = System.Drawing.Color.LightGreen;
            this.barButtonRestartOnUpdate.ItemAppearance.Normal.ForeColor = System.Drawing.Color.Black;
            this.barButtonRestartOnUpdate.ItemAppearance.Normal.Options.UseBackColor = true;
            this.barButtonRestartOnUpdate.ItemAppearance.Normal.Options.UseForeColor = true;
            this.barButtonRestartOnUpdate.Name = "barButtonRestartOnUpdate";
            this.barButtonRestartOnUpdate.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barButtonRestartOnUpdate.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.BarButtonRestartOnUpdate_ItemClick);
            //
            // barButtonItemViews
            //
            this.barButtonItemViews.ActAsDropDown = true;
            this.barButtonItemViews.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
            this.barButtonItemViews.Caption = "Views";
            this.barButtonItemViews.DropDownControl = this.popupMenuDockPanels;
            this.barButtonItemViews.Id = 262;
            this.barButtonItemViews.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Views;
            this.barButtonItemViews.Name = "barButtonItemViews";
            this.barButtonItemViews.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemDockPanels_ItemClick);
            //
            // barCheckImmediatePayment
            //
            this.barCheckImmediatePayment.AllowHtmlText = DevExpress.Utils.DefaultBoolean.True;
            this.barCheckImmediatePayment.Caption = "Immediate payment";
            this.barCheckImmediatePayment.CheckBoxVisibility = DevExpress.XtraBars.CheckBoxVisibility.BeforeText;
            this.barCheckImmediatePayment.Id = 113;
            this.barCheckImmediatePayment.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("barCheckImmediatePayment.ImageOptions.SvgImage")));
            this.barCheckImmediatePayment.ImageOptions.SvgImageSize = new System.Drawing.Size(32, 32);
            this.barCheckImmediatePayment.Name = "barCheckImmediatePayment";
            this.barCheckImmediatePayment.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barCheckImmediatePayment.CheckedChanged += new DevExpress.XtraBars.ItemClickEventHandler(this.barCheckPaypalPayment_CheckedChanged);
            //
            // barButtonItemShortcuts
            //
            this.barButtonItemShortcuts.Caption = "Shortcuts";
            this.barButtonItemShortcuts.Id = 62;
            this.barButtonItemShortcuts.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Shortcut;
            this.barButtonItemShortcuts.Name = "barButtonItemShortcuts";
            this.barButtonItemShortcuts.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemShortcuts_ItemClick);
            //
            // skinDropDownButtonItem1
            //
            this.skinDropDownButtonItem1.Hint = "Theme";
            this.skinDropDownButtonItem1.Id = 235;
            this.skinDropDownButtonItem1.Name = "skinDropDownButtonItem1";
            //
            // barButtonItemOpenInBrowser
            //
            this.barButtonItemOpenInBrowser.ActAsDropDown = true;
            this.barButtonItemOpenInBrowser.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
            this.barButtonItemOpenInBrowser.Caption = "Open in browser";
            this.barButtonItemOpenInBrowser.DropDownControl = this.popupMenuOpenInBrowser;
            this.barButtonItemOpenInBrowser.Id = 334;
            this.barButtonItemOpenInBrowser.Name = "barButtonItemOpenInBrowser";
            toolTipTitleItem8.Text = "Auto open item page in browser.";
            superToolTip19.Items.Add(toolTipTitleItem8);
            this.barButtonItemOpenInBrowser.SuperTip = superToolTip19;
            //
            // popupMenuOpenInBrowser
            //
            this.popupMenuOpenInBrowser.ItemLinks.Add(this.barCheckItemNone);
            this.popupMenuOpenInBrowser.Name = "popupMenuOpenInBrowser";
            this.popupMenuOpenInBrowser.Ribbon = this.ribbonControl1;
            this.popupMenuOpenInBrowser.BeforePopup += new System.ComponentModel.CancelEventHandler(this.PopupMenuOpenInBrowser_BeforePopup);
            //
            // barCheckNoConfirmations
            //
            this.barCheckNoConfirmations.Caption = "No confirmations";
            this.barCheckNoConfirmations.CheckBoxVisibility = DevExpress.XtraBars.CheckBoxVisibility.BeforeText;
            this.barCheckNoConfirmations.Id = 23311;
            this.barCheckNoConfirmations.Name = "barCheckNoConfirmations";
            this.barCheckNoConfirmations.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barCheckNoConfirmations.CheckedChanged += new DevExpress.XtraBars.ItemClickEventHandler(this.BarCheckNoConfirmations_CheckedChanged);
            //
            // barButtonItemTelegram
            //
            this.barButtonItemTelegram.Caption = "Telegram";
            this.barButtonItemTelegram.Id = 236;
            this.barButtonItemTelegram.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.telegram;
            this.barButtonItemTelegram.Name = "barButtonItemTelegram";
            this.barButtonItemTelegram.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.SmallWithText;
            toolTipItem18.Text = "Push notification to a your Telegram on any device.";
            superToolTip20.Items.Add(toolTipItem18);
            this.barButtonItemTelegram.SuperTip = superToolTip20;
            this.barButtonItemTelegram.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemTelegram_ItemClick);
            //
            // barEditItemDescriptionColor
            //
            this.barEditItemDescriptionColor.Caption = "Color";
            this.barEditItemDescriptionColor.Edit = this.repositoryItemColorEditDescription;
            this.barEditItemDescriptionColor.Hint = "Change background color for item Description";
            this.barEditItemDescriptionColor.Id = 354;
            this.barEditItemDescriptionColor.Name = "barEditItemDescriptionColor";
            this.barEditItemDescriptionColor.EditValueChanged += new System.EventHandler(this.colorPickBrowser_EditValueChanged);
            //
            // repositoryItemColorEditDescription
            //
            this.repositoryItemColorEditDescription.AutoHeight = false;
            this.repositoryItemColorEditDescription.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemColorEditDescription.Name = "repositoryItemColorEditDescription";
            //
            // barEditItemDescriptionZoom
            //
            this.barEditItemDescriptionZoom.Caption = "Zoom";
            this.barEditItemDescriptionZoom.Edit = this.repositoryItemSpinEditDescriptionZoom;
            this.barEditItemDescriptionZoom.EditValue = "100";
            this.barEditItemDescriptionZoom.Hint = "Increase/Decrease font size for item Description";
            this.barEditItemDescriptionZoom.Id = 471;
            this.barEditItemDescriptionZoom.Name = "barEditItemDescriptionZoom";
            this.barEditItemDescriptionZoom.EditValueChanged += new System.EventHandler(this.zoomTrackBarBrowser_EditValueChanged);
            //
            // repositoryItemSpinEditDescriptionZoom
            //
            this.repositoryItemSpinEditDescriptionZoom.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.repositoryItemSpinEditDescriptionZoom.AutoHeight = false;
            this.repositoryItemSpinEditDescriptionZoom.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemSpinEditDescriptionZoom.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.repositoryItemSpinEditDescriptionZoom.IsFloatValue = false;
            this.repositoryItemSpinEditDescriptionZoom.MaskSettings.Set("mask", "N00");
            this.repositoryItemSpinEditDescriptionZoom.MaxValue = new decimal(new int[] {
            400,
            0,
            0,
            0});
            this.repositoryItemSpinEditDescriptionZoom.MinValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.repositoryItemSpinEditDescriptionZoom.Name = "repositoryItemSpinEditDescriptionZoom";
            //
            // barEditItemProfile
            //
            this.barEditItemProfile.Caption = "Profile";
            this.barEditItemProfile.Edit = this.repositoryItemComboBoxProfile;
            this.barEditItemProfile.EditWidth = 100;
            this.barEditItemProfile.Id = 84;
            this.barEditItemProfile.Name = "barEditItemProfile";
            this.barEditItemProfile.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barEditItemProfile.EditValueChanged += new System.EventHandler(this.barEditItemProfile_EditValueChanged);
            //
            // repositoryItemComboBoxProfile
            //
            this.repositoryItemComboBoxProfile.AutoComplete = false;
            this.repositoryItemComboBoxProfile.AutoHeight = false;
            this.repositoryItemComboBoxProfile.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemComboBoxProfile.Name = "repositoryItemComboBoxProfile";
            this.repositoryItemComboBoxProfile.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            //
            // barEditItemInitialResultsLimit
            //
            this.barEditItemInitialResultsLimit.Caption = "Initial Results Max";
            this.barEditItemInitialResultsLimit.Edit = this.repositoryItemSpinEditInitialSearch;
            this.barEditItemInitialResultsLimit.EditValue = "20";
            this.barEditItemInitialResultsLimit.Hint = resources.GetString("barEditItemInitialResultsLimit.Hint");
            this.barEditItemInitialResultsLimit.Id = 491;
            this.barEditItemInitialResultsLimit.Name = "barEditItemInitialResultsLimit";
            this.barEditItemInitialResultsLimit.EditValueChanged += new System.EventHandler(this.barEditItemInitialResultsLimit_EditValueChanged);
            //
            // repositoryItemSpinEditInitialSearch
            //
            this.repositoryItemSpinEditInitialSearch.AutoHeight = false;
            this.repositoryItemSpinEditInitialSearch.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemSpinEditInitialSearch.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.repositoryItemSpinEditInitialSearch.MaskSettings.Set("mask", "D");
            this.repositoryItemSpinEditInitialSearch.MaskSettings.Set("autoHideDecimalSeparator", true);
            this.repositoryItemSpinEditInitialSearch.MaxValue = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.repositoryItemSpinEditInitialSearch.Name = "repositoryItemSpinEditInitialSearch";
            this.repositoryItemSpinEditInitialSearch.UseMaskAsDisplayFormat = true;
            //
            // barButtonItemIgnoreSellers
            //
            this.barButtonItemIgnoreSellers.Caption = "Blocked Sellers";
            this.barButtonItemIgnoreSellers.Id = 510;
            this.barButtonItemIgnoreSellers.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Ignore_Seller;
            this.barButtonItemIgnoreSellers.Name = "barButtonItemIgnoreSellers";
            this.barButtonItemIgnoreSellers.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemIgnoreSellers_ItemClick);
            //
            // barButtonItemRestoreBackup
            //
            this.barButtonItemRestoreBackup.Caption = "Restore backup";
            this.barButtonItemRestoreBackup.Id = 311;
            this.barButtonItemRestoreBackup.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("barButtonItemRestoreBackup.ImageOptions.SvgImage")));
            this.barButtonItemRestoreBackup.Name = "barButtonItemRestoreBackup";
            this.barButtonItemRestoreBackup.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemRestoreBackup_ItemClick);
            //
            // barButtonItemCreateBackup
            //
            this.barButtonItemCreateBackup.Caption = "Create backup";
            this.barButtonItemCreateBackup.Id = 312;
            this.barButtonItemCreateBackup.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("barButtonItemCreateBackup.ImageOptions.SvgImage")));
            this.barButtonItemCreateBackup.Name = "barButtonItemCreateBackup";
            this.barButtonItemCreateBackup.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemCreateBackup_ItemClick);
            //
            // barButtonItemChangelog
            //
            this.barButtonItemChangelog.Caption = "Changelog";
            this.barButtonItemChangelog.Id = 13;
            this.barButtonItemChangelog.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("barButtonItemChangelog.ImageOptions.SvgImage")));
            this.barButtonItemChangelog.Name = "barButtonItemChangelog";
            this.barButtonItemChangelog.Tag = "https://ubuyfirst.com/docs/changelog/";
            this.barButtonItemChangelog.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.OpenTagLinkByButton);
            //
            // barCheckItemTotalPriceAsMaxPrice
            //
            this.barCheckItemTotalPriceAsMaxPrice.Caption = "Total Price as Max Price";
            this.barCheckItemTotalPriceAsMaxPrice.CheckBoxVisibility = DevExpress.XtraBars.CheckBoxVisibility.BeforeText;
            this.barCheckItemTotalPriceAsMaxPrice.Id = 16;
            this.barCheckItemTotalPriceAsMaxPrice.Name = "barCheckItemTotalPriceAsMaxPrice";
            toolTipTitleItem9.Text = "Use Total Price for Max Price instead of Item Price";
            superToolTip21.Items.Add(toolTipTitleItem9);
            superToolTip21.Items.Add(toolTipItem19);
            this.barCheckItemTotalPriceAsMaxPrice.SuperTip = superToolTip21;
            //
            // barCheckItemPriceOpensCheckout
            //
            this.barCheckItemPriceOpensCheckout.Caption = "Click Price cell to open Checkout";
            this.barCheckItemPriceOpensCheckout.CheckBoxVisibility = DevExpress.XtraBars.CheckBoxVisibility.BeforeText;
            this.barCheckItemPriceOpensCheckout.Id = 17;
            this.barCheckItemPriceOpensCheckout.Name = "barCheckItemPriceOpensCheckout";
            toolTipItem20.Text = "Click on a Item Price or Total price will open eBay Checkout page, not Item page." +
    "";
            superToolTip22.Items.Add(toolTipItem20);
            this.barCheckItemPriceOpensCheckout.SuperTip = superToolTip22;
            this.barCheckItemPriceOpensCheckout.CheckedChanged += new DevExpress.XtraBars.ItemClickEventHandler(this.barCheckItemPriceOpensCheckout_CheckedChanged);
            //
            // barButtonItemSync
            //
            this.barButtonItemSync.Caption = "Sync search terms";
            this.barButtonItemSync.Id = 18;
            this.barButtonItemSync.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("barButtonItemSync.ImageOptions.SvgImage")));
            this.barButtonItemSync.Name = "barButtonItemSync";
            toolTipItem21.Text = "Syncronize your search term from google spreadsheet or your custom csv url.";
            superToolTip23.Items.Add(toolTipItem21);
            this.barButtonItemSync.SuperTip = superToolTip23;
            this.barButtonItemSync.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemSync_ItemClick);
            //
            // barButtonItemGetExternalData
            //
            this.barButtonItemGetExternalData.Caption = "Get Data";
            this.barButtonItemGetExternalData.Id = 119;
            this.barButtonItemGetExternalData.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("barButtonItemGetExternalData.ImageOptions.SvgImage")));
            this.barButtonItemGetExternalData.Name = "barButtonItemGetExternalData";
            this.barButtonItemGetExternalData.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemGetExternalData_ItemClick);
            //
            // barEditItemResultsMaxCount
            //
            this.barEditItemResultsMaxCount.Caption = "Keep Results Max";
            this.barEditItemResultsMaxCount.Edit = this.repositoryItemResultsMaxCount;
            this.barEditItemResultsMaxCount.EditValue = "5000";
            this.barEditItemResultsMaxCount.Hint = "Keep maximum N number of items in the Results grid. Old results that are over lim" +
    "it will become hidden.";
            this.barEditItemResultsMaxCount.Id = 2011;
            this.barEditItemResultsMaxCount.Name = "barEditItemResultsMaxCount";
            this.barEditItemResultsMaxCount.EditValueChanged += new System.EventHandler(this.barEditItemResultsMaxCount_EditValueChanged);
            //
            // repositoryItemResultsMaxCount
            //
            this.repositoryItemResultsMaxCount.AutoHeight = false;
            this.repositoryItemResultsMaxCount.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemResultsMaxCount.Increment = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.repositoryItemResultsMaxCount.MaskSettings.Set("autoHideDecimalSeparator", true);
            this.repositoryItemResultsMaxCount.MaskSettings.Set("mask", "D");
            this.repositoryItemResultsMaxCount.MaxValue = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.repositoryItemResultsMaxCount.MinValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.repositoryItemResultsMaxCount.Name = "repositoryItemResultsMaxCount";
            this.repositoryItemResultsMaxCount.UseMaskAsDisplayFormat = true;
            //
            // barCheckItemRestock
            //
            this.barCheckItemRestock.Caption = "Restock Enabled";
            this.barCheckItemRestock.CheckBoxVisibility = DevExpress.XtraBars.CheckBoxVisibility.BeforeText;
            this.barCheckItemRestock.Id = 12321;
            this.barCheckItemRestock.Name = "barCheckItemRestock";
            this.barCheckItemRestock.CheckedChanged += new DevExpress.XtraBars.ItemClickEventHandler(this.barCheckItemRestock_CheckedChanged);
            //
            // barButtonItemRestockReport
            //
            this.barButtonItemRestockReport.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Left;
            this.barButtonItemRestockReport.Caption = "Report";
            this.barButtonItemRestockReport.Id = 12322;
            this.barButtonItemRestockReport.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("barButtonItemRestockReport.ImageOptions.SvgImage")));
            this.barButtonItemRestockReport.Name = "barButtonItemRestockReport";
            this.barButtonItemRestockReport.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.SmallWithText;
            this.barButtonItemRestockReport.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemRestockReport_ItemClick);
            //
            // barStaticItemRestock
            //
            this.barStaticItemRestock.Id = 23;
            this.barStaticItemRestock.Name = "barStaticItemRestock";
            //
            // barEditItemDailySpendLimit
            //
            this.barEditItemDailySpendLimit.AutoFillWidthInMenu = DevExpress.Utils.DefaultBoolean.True;
            this.barEditItemDailySpendLimit.Caption = "Daily Spend Limit ";
            this.barEditItemDailySpendLimit.Edit = this.repositoryItemSpinEditDailySpendLimit;
            this.barEditItemDailySpendLimit.EditValue = "10000";
            this.barEditItemDailySpendLimit.EditWidth = 90;
            this.barEditItemDailySpendLimit.Id = 24001;
            this.barEditItemDailySpendLimit.Name = "barEditItemDailySpendLimit";
            this.barEditItemDailySpendLimit.EditValueChanged += new System.EventHandler(this.barEditItemDailySpendLimit_EditValueChanged);
            //
            // repositoryItemSpinEditDailySpendLimit
            //
            this.repositoryItemSpinEditDailySpendLimit.AutoHeight = false;
            this.repositoryItemSpinEditDailySpendLimit.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemSpinEditDailySpendLimit.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.repositoryItemSpinEditDailySpendLimit.EditFormat.FormatString = "c";
            this.repositoryItemSpinEditDailySpendLimit.Increment = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.repositoryItemSpinEditDailySpendLimit.MaskSettings.Set("mask", "c0");
            this.repositoryItemSpinEditDailySpendLimit.MaskSettings.Set("hideInsignificantZeros", true);
            this.repositoryItemSpinEditDailySpendLimit.MaxValue = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.repositoryItemSpinEditDailySpendLimit.Name = "repositoryItemSpinEditDailySpendLimit";
            this.repositoryItemSpinEditDailySpendLimit.UseMaskAsDisplayFormat = true;
            //
            // barStaticItemTodaySpent
            //
            this.barStaticItemTodaySpent.Caption = "Spent today: $0";
            this.barStaticItemTodaySpent.Id = 25001;
            this.barStaticItemTodaySpent.Name = "barStaticItemTodaySpent";
            //
            // ribbonPage1
            //
            this.ribbonPage1.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroupHome,
            this.ribbonPageGroupKeywordsFilters,
            this.ribbonPageGroupResults});
            this.ribbonPage1.Name = "ribbonPage1";
            this.ribbonPage1.Text = "Home";
            //
            // ribbonPageGroupHome
            //
            this.ribbonPageGroupHome.AllowTextClipping = false;
            this.ribbonPageGroupHome.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroupHome.ItemLinks.Add(this.barButtonStart);
            this.ribbonPageGroupHome.ItemLinks.Add(this.barButtonClear);
            this.ribbonPageGroupHome.ItemLinks.Add(this.barButtonBuy);
            this.ribbonPageGroupHome.ItemLinks.Add(this.barButtonMakeOffer);
            this.ribbonPageGroupHome.Name = "ribbonPageGroupHome";
            this.ribbonPageGroupHome.Text = "Main";
            //
            // ribbonPageGroupKeywordsFilters
            //
            this.ribbonPageGroupKeywordsFilters.AllowTextClipping = false;
            this.ribbonPageGroupKeywordsFilters.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroupKeywordsFilters.ItemLinks.Add(this.barButtonItemKeywords);
            this.ribbonPageGroupKeywordsFilters.ItemLinks.Add(this.barButtonItemFilters);
            this.ribbonPageGroupKeywordsFilters.Name = "ribbonPageGroupKeywordsFilters";
            this.ribbonPageGroupKeywordsFilters.Text = "Keywords";
            //
            // ribbonPageGroupResults
            //
            this.ribbonPageGroupResults.AllowTextClipping = false;
            this.ribbonPageGroupResults.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroupResults.ItemLinks.Add(this.barButtonItemViews);
            this.ribbonPageGroupResults.Name = "ribbonPageGroupResults";
            this.ribbonPageGroupResults.Text = "Results";
            //
            // ribbonPageGrid
            //
            this.ribbonPageGrid.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroupItemDetails,
            this.ribbonPageGroupAppearance,
            this.ribbonPageGroupTopRow,
            this.ribbonPageGroupResultsLimit,
            this.ribbonPageGroupInitialResults,
            this.ribbonPageGroupShortcuts,
            this.ribbonPageGroupIgnoreSeller});
            this.ribbonPageGrid.Name = "ribbonPageGrid";
            this.ribbonPageGrid.Text = "Grid";
            //
            // ribbonPageGroupItemDetails
            //
            this.ribbonPageGroupItemDetails.AllowTextClipping = false;
            this.ribbonPageGroupItemDetails.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroupItemDetails.ItemLinks.Add(this.barButtonCustomColumns);
            this.ribbonPageGroupItemDetails.Name = "ribbonPageGroupItemDetails";
            this.ribbonPageGroupItemDetails.Text = "Item details";
            //
            // ribbonPageGroupAppearance
            //
            this.ribbonPageGroupAppearance.AllowTextClipping = false;
            this.ribbonPageGroupAppearance.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroupAppearance.ItemLinks.Add(this.barButtonItemGridFont);
            this.ribbonPageGroupAppearance.ItemLinks.Add(this.barEditItemRowHeight);
            this.ribbonPageGroupAppearance.Name = "ribbonPageGroupAppearance";
            this.ribbonPageGroupAppearance.Text = "Appearance";
            //
            // ribbonPageGroupTopRow
            //
            this.ribbonPageGroupTopRow.AllowTextClipping = false;
            this.ribbonPageGroupTopRow.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroupTopRow.ItemLinks.Add(this.barEditItemAutoSelect);
            this.ribbonPageGroupTopRow.Name = "ribbonPageGroupTopRow";
            this.ribbonPageGroupTopRow.Text = "Top row";
            //
            // ribbonPageGroupResultsLimit
            //
            this.ribbonPageGroupResultsLimit.ItemLinks.Add(this.barEditItemInitialResultsLimit);
            this.ribbonPageGroupResultsLimit.ItemLinks.Add(this.barEditItemResultsMaxCount);
            this.ribbonPageGroupResultsLimit.Name = "ribbonPageGroupResultsLimit";
            this.ribbonPageGroupResultsLimit.Text = "Results Limit";
            //
            // ribbonPageGroupInitialResults
            //
            this.ribbonPageGroupInitialResults.ItemLinks.Add(this.barCheckItemTotalPriceAsMaxPrice);
            this.ribbonPageGroupInitialResults.ItemLinks.Add(this.barCheckItemPriceOpensCheckout);
            this.ribbonPageGroupInitialResults.Name = "ribbonPageGroupInitialResults";
            this.ribbonPageGroupInitialResults.Text = "Other";
            //
            // ribbonPageGroupShortcuts
            //
            this.ribbonPageGroupShortcuts.AllowTextClipping = false;
            this.ribbonPageGroupShortcuts.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroupShortcuts.ItemLinks.Add(this.barButtonItemShortcuts);
            this.ribbonPageGroupShortcuts.Name = "ribbonPageGroupShortcuts";
            this.ribbonPageGroupShortcuts.Text = "Shortcuts";
            //
            // ribbonPageGroupIgnoreSeller
            //
            this.ribbonPageGroupIgnoreSeller.ItemLinks.Add(this.barButtonItemIgnoreSellers);
            this.ribbonPageGroupIgnoreSeller.Name = "ribbonPageGroupIgnoreSeller";
            this.ribbonPageGroupIgnoreSeller.Text = "Sellers";
            //
            // ribbonPageDescriptionSettings
            //
            this.ribbonPageDescriptionSettings.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroupHighLightWords,
            this.ribbonPageGroupColor,
            this.ribbonPageGroupDescriptionZoom});
            this.ribbonPageDescriptionSettings.Name = "ribbonPageDescriptionSettings";
            this.ribbonPageDescriptionSettings.Text = "Description";
            //
            // ribbonPageGroupHighLightWords
            //
            this.ribbonPageGroupHighLightWords.AllowTextClipping = false;
            this.ribbonPageGroupHighLightWords.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroupHighLightWords.ItemLinks.Add(this.barButtonItemHightlightWords);
            this.ribbonPageGroupHighLightWords.Name = "ribbonPageGroupHighLightWords";
            this.ribbonPageGroupHighLightWords.Text = "Highlight";
            //
            // ribbonPageGroupColor
            //
            this.ribbonPageGroupColor.ItemLinks.Add(this.barEditItemDescriptionColor);
            this.ribbonPageGroupColor.Name = "ribbonPageGroupColor";
            this.ribbonPageGroupColor.Text = "Color";
            //
            // ribbonPageGroupDescriptionZoom
            //
            this.ribbonPageGroupDescriptionZoom.ItemLinks.Add(this.barEditItemDescriptionZoom);
            this.ribbonPageGroupDescriptionZoom.Name = "ribbonPageGroupDescriptionZoom";
            this.ribbonPageGroupDescriptionZoom.Text = "Zoom";
            //
            // ribbonPageApplicationOptions
            //
            this.ribbonPageApplicationOptions.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroupEBayAccounts,
            this.ribbonPageGroupNewItem,
            this.ribbonPageGroupIdleTimeout,
            this.ribbonPageGroupTimeSync,
            this.ribbonPageGroupProgramLaunch});
            this.ribbonPageApplicationOptions.Name = "ribbonPageApplicationOptions";
            this.ribbonPageApplicationOptions.Text = "Options";
            //
            // ribbonPageGroupEBayAccounts
            //
            this.ribbonPageGroupEBayAccounts.AllowTextClipping = false;
            this.ribbonPageGroupEBayAccounts.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroupEBayAccounts.ItemLinks.Add(this.barButtonEbayAccounts);
            this.ribbonPageGroupEBayAccounts.Name = "ribbonPageGroupEBayAccounts";
            this.ribbonPageGroupEBayAccounts.Text = "Authorization";
            //
            // ribbonPageGroupNewItem
            //
            this.ribbonPageGroupNewItem.AllowTextClipping = false;
            this.ribbonPageGroupNewItem.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroupNewItem.ItemLinks.Add(this.barButtonItemOpenInBrowser);
            this.ribbonPageGroupNewItem.ItemLinks.Add(this.barCheckItemMaximizewindow);
            this.ribbonPageGroupNewItem.ItemLinks.Add(this.barCheckItemSoundAlert, true);
            this.ribbonPageGroupNewItem.ItemLinks.Add(this.barButtonItemSelectSound);
            this.ribbonPageGroupNewItem.ItemLinks.Add(this.barButtonItemTrayAlert, true);
            this.ribbonPageGroupNewItem.ItemLinks.Add(this.barButtonItemPushBullet);
            this.ribbonPageGroupNewItem.ItemLinks.Add(this.barButtonItemTelegram);
            this.ribbonPageGroupNewItem.ItemsLayout = DevExpress.XtraBars.Ribbon.RibbonPageGroupItemsLayout.TwoRows;
            this.ribbonPageGroupNewItem.Name = "ribbonPageGroupNewItem";
            this.ribbonPageGroupNewItem.Text = "New Item";
            //
            // ribbonPageGroupIdleTimeout
            //
            this.ribbonPageGroupIdleTimeout.AllowTextClipping = false;
            this.ribbonPageGroupIdleTimeout.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroupIdleTimeout.ItemLinks.Add(this.barEnableIdleTimeout);
            this.ribbonPageGroupIdleTimeout.ItemLinks.Add(this.barIdleTimeoutMinutes);
            this.ribbonPageGroupIdleTimeout.Name = "ribbonPageGroupIdleTimeout";
            this.ribbonPageGroupIdleTimeout.Text = "Pause";
            //
            // ribbonPageGroupTimeSync
            //
            this.ribbonPageGroupTimeSync.AllowTextClipping = false;
            this.ribbonPageGroupTimeSync.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroupTimeSync.ItemLinks.Add(this.barStaticItemTimeDiffText);
            this.ribbonPageGroupTimeSync.ItemLinks.Add(this.barButtonItemTimeSync);
            this.ribbonPageGroupTimeSync.ItemLinks.Add(this.barEditItemTimeZone);
            this.ribbonPageGroupTimeSync.Name = "ribbonPageGroupTimeSync";
            this.ribbonPageGroupTimeSync.Text = "Time";
            //
            // ribbonPageGroupProgramLaunch
            //
            this.ribbonPageGroupProgramLaunch.AllowTextClipping = false;
            this.ribbonPageGroupProgramLaunch.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroupProgramLaunch.ItemLinks.Add(this.barCheckItemStartOnBoot);
            this.ribbonPageGroupProgramLaunch.ItemLinks.Add(this.barCheckItemAutoStartSearch);
            this.ribbonPageGroupProgramLaunch.Name = "ribbonPageGroupProgramLaunch";
            this.ribbonPageGroupProgramLaunch.Text = "Program launch";
            //
            // ribbonPageSync
            //
            this.ribbonPageSync.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroupSync});
            this.ribbonPageSync.Name = "ribbonPageSync";
            this.ribbonPageSync.Text = "Sync";
            this.ribbonPageSync.Visible = false;
            //
            // ribbonPageGroupSync
            //
            this.ribbonPageGroupSync.ItemLinks.Add(this.barButtonItemSync);
            this.ribbonPageGroupSync.Name = "ribbonPageGroupSync";
            this.ribbonPageGroupSync.Text = "Sync";
            //
            // ribbonPageData
            //
            this.ribbonPageData.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroupExternalData});
            this.ribbonPageData.Name = "ribbonPageData";
            this.ribbonPageData.Text = "Data";
            this.ribbonPageData.Visible = false;
            //
            // ribbonPageGroupExternalData
            //
            this.ribbonPageGroupExternalData.ItemLinks.Add(this.barButtonItemGetExternalData);
            this.ribbonPageGroupExternalData.Name = "ribbonPageGroupExternalData";
            this.ribbonPageGroupExternalData.Text = "Data";
            //
            // ribbonPageView
            //
            this.ribbonPageView.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroupSkin,
            this.ribbonPageGroupLayout});
            this.ribbonPageView.Name = "ribbonPageView";
            this.ribbonPageView.Text = "View";
            //
            // ribbonPageGroupSkin
            //
            this.ribbonPageGroupSkin.AllowTextClipping = false;
            this.ribbonPageGroupSkin.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroupSkin.ItemLinks.Add(this.skinDropDownButtonItem1);
            this.ribbonPageGroupSkin.Name = "ribbonPageGroupSkin";
            this.ribbonPageGroupSkin.Text = "Skin";
            //
            // ribbonPageGroupLayout
            //
            this.ribbonPageGroupLayout.AllowTextClipping = false;
            this.ribbonPageGroupLayout.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroupLayout.ItemLinks.Add(this.barButtonItemDockPanels);
            this.ribbonPageGroupLayout.ItemLinks.Add(this.menuDocking);
            this.ribbonPageGroupLayout.ItemLinks.Add(this.barWorkspaceMenuItem);
            this.ribbonPageGroupLayout.ItemLinks.Add(this.barButtonResetWorkspace);
            this.ribbonPageGroupLayout.Name = "ribbonPageGroupLayout";
            this.ribbonPageGroupLayout.Text = "Layout";
            //
            // ribbonPageCheckout
            //
            this.ribbonPageCheckout.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroupCheckout,
            this.ribbonPageGroupProfile,
            this.ribbonPageGroupRestock});
            this.ribbonPageCheckout.Name = "ribbonPageCheckout";
            this.ribbonPageCheckout.Text = "Checkout";
            this.ribbonPageCheckout.Visible = false;
            //
            // ribbonPageGroupCheckout
            //
            this.ribbonPageGroupCheckout.ItemLinks.Add(this.barCheckImmediatePayment);
            this.ribbonPageGroupCheckout.ItemLinks.Add(this.barCheckNoConfirmations);
            this.ribbonPageGroupCheckout.Name = "ribbonPageGroupCheckout";
            this.ribbonPageGroupCheckout.Text = "Checkout";
            //
            // ribbonPageGroupProfile
            //
            this.ribbonPageGroupProfile.ItemLinks.Add(this.barEditItemProfile);
            this.ribbonPageGroupProfile.Name = "ribbonPageGroupProfile";
            this.ribbonPageGroupProfile.Text = "Chrome Profile";
            //
            // ribbonPageGroupRestock
            //
            this.ribbonPageGroupRestock.ItemLinks.Add(this.barCheckItemRestock);
            this.ribbonPageGroupRestock.ItemLinks.Add(this.barButtonItemRestockReport);
            this.ribbonPageGroupRestock.ItemLinks.Add(this.barStaticItemTodaySpent, true);
            this.ribbonPageGroupRestock.ItemLinks.Add(this.barEditItemDailySpendLimit);
            this.ribbonPageGroupRestock.Name = "ribbonPageGroupRestock";
            this.ribbonPageGroupRestock.Text = "Restock";
            //
            // ribbonPageHelp
            //
            this.ribbonPageHelp.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroupSupport,
            this.ribbonPageGroup2,
            this.ribbonPageGroup4});
            this.ribbonPageHelp.Name = "ribbonPageHelp";
            this.ribbonPageHelp.Text = "Help";
            //
            // ribbonPageGroupSupport
            //
            this.ribbonPageGroupSupport.AllowTextClipping = false;
            this.ribbonPageGroupSupport.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroupSupport.ItemLinks.Add(this.barButtonSubscriptionInfo);
            this.ribbonPageGroupSupport.ItemLinks.Add(this.barButtonSupport);
            this.ribbonPageGroupSupport.ItemLinks.Add(this.barButtonTermsConditions);
            this.ribbonPageGroupSupport.ItemLinks.Add(this.barButtonItemChangelog);
            this.ribbonPageGroupSupport.ItemLinks.Add(this.barButtonItemHelp);
            this.ribbonPageGroupSupport.Name = "ribbonPageGroupSupport";
            this.ribbonPageGroupSupport.Text = "Support";
            //
            // ribbonPageGroup2
            //
            this.ribbonPageGroup2.AllowTextClipping = false;
            this.ribbonPageGroup2.ItemLinks.Add(this.barButtonItemCreateBackup);
            this.ribbonPageGroup2.ItemLinks.Add(this.barButtonItemRestoreBackup);
            this.ribbonPageGroup2.Name = "ribbonPageGroup2";
            this.ribbonPageGroup2.Text = "Backup / Restore";
            //
            // ribbonPageGroup4
            //
            this.ribbonPageGroup4.AllowTextClipping = false;
            this.ribbonPageGroup4.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroup4.ItemLinks.Add(this.barStaticBuildVersion);
            this.ribbonPageGroup4.ItemLinks.Add(this.barEditItemHwid);
            this.ribbonPageGroup4.Name = "ribbonPageGroup4";
            this.ribbonPageGroup4.Text = "Version";
            //
            // repositoryItemMemoExEdit1
            //
            this.repositoryItemMemoExEdit1.AutoHeight = false;
            this.repositoryItemMemoExEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemMemoExEdit1.Name = "repositoryItemMemoExEdit1";
            //
            // repositoryItemColorEdit1
            //
            this.repositoryItemColorEdit1.AutoHeight = false;
            this.repositoryItemColorEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemColorEdit1.Name = "repositoryItemColorEdit1";
            //
            // repositoryItemMemoEdit1
            //
            this.repositoryItemMemoEdit1.Name = "repositoryItemMemoEdit1";
            //
            // repositoryItemMemoExEdit2
            //
            this.repositoryItemMemoExEdit2.AutoHeight = false;
            this.repositoryItemMemoExEdit2.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemMemoExEdit2.Name = "repositoryItemMemoExEdit2";
            //
            // repositoryItemColorEdit2
            //
            this.repositoryItemColorEdit2.AutoHeight = false;
            this.repositoryItemColorEdit2.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemColorEdit2.Name = "repositoryItemColorEdit2";
            //
            // repositoryItemOfficeColorEdit1
            //
            this.repositoryItemOfficeColorEdit1.AutoHeight = false;
            this.repositoryItemOfficeColorEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemOfficeColorEdit1.Name = "repositoryItemOfficeColorEdit1";
            //
            // repositoryItemColorPickEdit1
            //
            this.repositoryItemColorPickEdit1.AutoHeight = false;
            this.repositoryItemColorPickEdit1.AutomaticColor = System.Drawing.Color.Black;
            this.repositoryItemColorPickEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemColorPickEdit1.Name = "repositoryItemColorPickEdit1";
            //
            // repositoryItemOfficeColorPickEdit1
            //
            this.repositoryItemOfficeColorPickEdit1.AutoHeight = false;
            this.repositoryItemOfficeColorPickEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemOfficeColorPickEdit1.Name = "repositoryItemOfficeColorPickEdit1";
            //
            // repositoryItemMemoEdit2
            //
            this.repositoryItemMemoEdit2.Name = "repositoryItemMemoEdit2";
            this.repositoryItemMemoEdit2.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            //
            // repositoryItemRichEditFontSizeEdit1
            //
            this.repositoryItemRichEditFontSizeEdit1.AutoHeight = false;
            this.repositoryItemRichEditFontSizeEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemRichEditFontSizeEdit1.Name = "repositoryItemRichEditFontSizeEdit1";
            //
            // repositoryItemFontEdit1
            //
            this.repositoryItemFontEdit1.AutoHeight = false;
            this.repositoryItemFontEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemFontEdit1.Name = "repositoryItemFontEdit1";
            //
            // repositoryItemFontStyle1
            //
            this.repositoryItemFontStyle1.AutoHeight = false;
            this.repositoryItemFontStyle1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemFontStyle1.Name = "repositoryItemFontStyle1";
            //
            // repositoryItemBorderLineStyle1
            //
            this.repositoryItemBorderLineStyle1.AutoHeight = false;
            this.repositoryItemBorderLineStyle1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemBorderLineStyle1.Name = "repositoryItemBorderLineStyle1";
            //
            // repositoryItemCheckEdit1
            //
            this.repositoryItemCheckEdit1.AutoHeight = false;
            this.repositoryItemCheckEdit1.Name = "repositoryItemCheckEdit1";
            //
            // ribbonStatusBar1
            //
            this.ribbonStatusBar1.ItemLinks.Add(this.barButtonSupport);
            this.ribbonStatusBar1.ItemLinks.Add(this.barButtonClear);
            this.ribbonStatusBar1.ItemLinks.Add(this.barButtonStart);
            this.ribbonStatusBar1.ItemLinks.Add(this.barStaticProgress);
            this.ribbonStatusBar1.ItemLinks.Add(this.barCheckItemPushBullet);
            this.ribbonStatusBar1.ItemLinks.Add(this.barStaticErrorsVal);
            this.ribbonStatusBar1.ItemLinks.Add(this.barStaticLicense);
            this.ribbonStatusBar1.ItemLinks.Add(this.barButtonRestartOnUpdate);
            this.ribbonStatusBar1.ItemLinks.Add(this.barStaticNotification);
            this.ribbonStatusBar1.ItemLinks.Add(this.barStaticItemRestock);
            this.ribbonStatusBar1.Location = new System.Drawing.Point(0, 557);
            this.ribbonStatusBar1.Margin = new System.Windows.Forms.Padding(2);
            this.ribbonStatusBar1.Name = "ribbonStatusBar1";
            this.ribbonStatusBar1.Ribbon = this.ribbonControl1;
            this.ribbonStatusBar1.Size = new System.Drawing.Size(1078, 24);
            //
            // ribbonStatusBar2
            //
            this.ribbonStatusBar2.Location = new System.Drawing.Point(0, 146);
            this.ribbonStatusBar2.Name = "ribbonStatusBar2";
            this.ribbonStatusBar2.Ribbon = this.ribbonControl1;
            this.ribbonStatusBar2.Size = new System.Drawing.Size(979, 27);
            //
            // btnCopyFilter
            //
            this.btnCopyFilter.ImageOptions.ImageIndex = 11;
            this.btnCopyFilter.ImageOptions.ImageList = this.imageList16;
            this.btnCopyFilter.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnCopyFilter.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("btnCopyFilter.ImageOptions.SvgImage")));
            this.btnCopyFilter.ImageOptions.SvgImageSize = new System.Drawing.Size(16, 16);
            this.btnCopyFilter.Location = new System.Drawing.Point(65, 6);
            this.btnCopyFilter.Name = "btnCopyFilter";
            this.btnCopyFilter.Size = new System.Drawing.Size(25, 25);
            this.btnCopyFilter.TabIndex = 3;
            this.btnCopyFilter.ToolTip = "Make a copy of selected filter.";
            this.btnCopyFilter.Click += new System.EventHandler(this.btnCopyFilter_Click);
            //
            // btnEditXfilter
            //
            this.btnEditXfilter.ImageOptions.ImageIndex = 3;
            this.btnEditXfilter.ImageOptions.ImageList = this.imageList16;
            this.btnEditXfilter.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnEditXfilter.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Edit;
            this.btnEditXfilter.ImageOptions.SvgImageSize = new System.Drawing.Size(16, 16);
            this.btnEditXfilter.Location = new System.Drawing.Point(35, 6);
            this.btnEditXfilter.Name = "btnEditXfilter";
            this.btnEditXfilter.Size = new System.Drawing.Size(25, 25);
            this.btnEditXfilter.TabIndex = 3;
            this.btnEditXfilter.ToolTip = "Edit selected filter.";
            this.btnEditXfilter.Click += new System.EventHandler(this.btnEditXfilter_Click);
            //
            // btnAddXfilter
            //
            this.btnAddXfilter.ImageOptions.AllowGlyphSkinning = DevExpress.Utils.DefaultBoolean.False;
            this.btnAddXfilter.ImageOptions.ImageIndex = 5;
            this.btnAddXfilter.ImageOptions.ImageList = this.imageList16;
            this.btnAddXfilter.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnAddXfilter.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Add;
            this.btnAddXfilter.ImageOptions.SvgImageSize = new System.Drawing.Size(26, 26);
            this.btnAddXfilter.Location = new System.Drawing.Point(5, 6);
            this.btnAddXfilter.Name = "btnAddXfilter";
            this.btnAddXfilter.Size = new System.Drawing.Size(25, 25);
            this.btnAddXfilter.TabIndex = 2;
            this.btnAddXfilter.Click += new System.EventHandler(this.btnAddXfilter_Click);
            //
            // lstchkXfilterList
            //
            this.lstchkXfilterList.AllowDrop = true;
            this.lstchkXfilterList.Cursor = System.Windows.Forms.Cursors.Default;
            this.lstchkXfilterList.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lstchkXfilterList.Location = new System.Drawing.Point(0, 37);
            this.lstchkXfilterList.Name = "lstchkXfilterList";
            this.lstchkXfilterList.SelectionMode = System.Windows.Forms.SelectionMode.MultiExtended;
            this.lstchkXfilterList.Size = new System.Drawing.Size(1048, 174);
            this.lstchkXfilterList.TabIndex = 1;
            this.lstchkXfilterList.DragDrop += new System.Windows.Forms.DragEventHandler(this.lstchkXfilterList_DragDrop);
            this.lstchkXfilterList.DragOver += new System.Windows.Forms.DragEventHandler(this.lstchkXfilterList_DragOver);
            this.lstchkXfilterList.DoubleClick += new System.EventHandler(this.lstchkXfilterList_DoubleClick);
            this.lstchkXfilterList.MouseDown += new System.Windows.Forms.MouseEventHandler(this.lstchkXfilterList_MouseDown);
            this.lstchkXfilterList.MouseMove += new System.Windows.Forms.MouseEventHandler(this.lstchkXfilterList_MouseMove);
            //
            // btnRemoveXfilter
            //
            this.btnRemoveXfilter.ImageOptions.ImageIndex = 1;
            this.btnRemoveXfilter.ImageOptions.ImageList = this.imageList16;
            this.btnRemoveXfilter.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnRemoveXfilter.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Remove;
            this.btnRemoveXfilter.ImageOptions.SvgImageSize = new System.Drawing.Size(16, 16);
            this.btnRemoveXfilter.Location = new System.Drawing.Point(207, 6);
            this.btnRemoveXfilter.Name = "btnRemoveXfilter";
            this.btnRemoveXfilter.Size = new System.Drawing.Size(25, 25);
            this.btnRemoveXfilter.TabIndex = 2;
            this.btnRemoveXfilter.ToolTip = "Delete selected filter.";
            this.btnRemoveXfilter.Click += new System.EventHandler(this.btnRemoveXfilter_Click);
            //
            // zoomTrackBarPictures
            //
            this.zoomTrackBarPictures.EditValue = 100;
            this.zoomTrackBarPictures.Location = new System.Drawing.Point(7, 38);
            this.zoomTrackBarPictures.Name = "zoomTrackBarPictures";
            this.zoomTrackBarPictures.Properties.AllowFocused = false;
            this.zoomTrackBarPictures.Properties.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.zoomTrackBarPictures.Properties.Appearance.Options.UseBackColor = true;
            this.zoomTrackBarPictures.Properties.AutoSize = false;
            this.zoomTrackBarPictures.Properties.LargeChange = 20;
            this.zoomTrackBarPictures.Properties.Maximum = 700;
            this.zoomTrackBarPictures.Properties.Middle = 362;
            this.zoomTrackBarPictures.Properties.Minimum = 25;
            this.zoomTrackBarPictures.Properties.Orientation = System.Windows.Forms.Orientation.Vertical;
            this.zoomTrackBarPictures.Properties.SmallChange = 20;
            this.zoomTrackBarPictures.Size = new System.Drawing.Size(30, 100);
            this.zoomTrackBarPictures.TabIndex = 19;
            this.zoomTrackBarPictures.ToolTip = "Adjust image size";
            this.zoomTrackBarPictures.Value = 100;
            this.zoomTrackBarPictures.EditValueChanged += new System.EventHandler(this.zoomTrackBarControl1_EditValueChanged_1);
            //
            // galleryControl1
            //
            this.galleryControl1.Controller = this.barAndDockingController1;
            this.galleryControl1.Controls.Add(this.galleryControlClient1);
            this.galleryControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            //
            //
            //
            this.galleryControl1.Gallery.AllowFilter = false;
            this.galleryControl1.Gallery.AllowHoverImages = true;
            this.galleryControl1.Gallery.BackColor = System.Drawing.Color.Transparent;
            contextButton1.AlignmentOptions.Position = DevExpress.Utils.ContextItemPosition.Far;
            contextButton1.Id = new System.Guid("86317960-e168-41db-bb00-4b65f7c29c98");
            contextButton1.ImageOptionsCollection.ItemHovered.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image")));
            contextButton1.ImageOptionsCollection.ItemNormal.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image1")));
            contextButton1.Name = "ContextButton";
            contextButton1.ToolTipIconType = DevExpress.Utils.ToolTipIconType.Information;
            contextButton1.ToolTipTitle = "Double click image to load larger image";
            contextButton1.Visibility = DevExpress.Utils.ContextItemVisibility.Hidden;
            this.galleryControl1.Gallery.ContextButtons.Add(contextButton1);
            this.galleryControl1.Gallery.FixedHoverImageSize = false;
            galleryItemGroup1.Caption = "Pictures";
            this.galleryControl1.Gallery.Groups.AddRange(new DevExpress.XtraBars.Ribbon.GalleryItemGroup[] {
            galleryItemGroup1});
            this.galleryControl1.Gallery.ImageSize = new System.Drawing.Size(160, 160);
            this.galleryControl1.Gallery.ItemImageLayout = DevExpress.Utils.Drawing.ImageLayoutMode.ZoomInside;
            this.galleryControl1.Gallery.ItemImageLocation = DevExpress.Utils.Locations.Top;
            this.galleryControl1.Gallery.OptionsImageLoad.RandomShow = false;
            this.galleryControl1.Gallery.ShowGroupCaption = false;
            this.galleryControl1.Gallery.UseMaxImageSize = true;
            this.galleryControl1.Gallery.ItemDoubleClick += new DevExpress.XtraBars.Ribbon.GalleryItemClickEventHandler(this.galleryControl1_Gallery_ItemDoubleClick);
            this.galleryControl1.Gallery.GalleryItemHover += new DevExpress.XtraBars.Ribbon.GalleryItemEventHandler(this.galleryControl1_Gallery_GalleryItemHover);
            this.galleryControl1.Location = new System.Drawing.Point(2, 2);
            this.galleryControl1.Margin = new System.Windows.Forms.Padding(2);
            this.galleryControl1.Name = "galleryControl1";
            this.galleryControl1.Size = new System.Drawing.Size(927, 163);
            this.galleryControl1.TabIndex = 18;
            this.galleryControl1.Text = "galleryControl1";
            //
            // galleryControlClient1
            //
            this.galleryControlClient1.GalleryControl = this.galleryControl1;
            this.galleryControlClient1.Location = new System.Drawing.Point(2, 2);
            this.galleryControlClient1.Margin = new System.Windows.Forms.Padding(2);
            this.galleryControlClient1.Size = new System.Drawing.Size(906, 159);
            //
            // picBoxEbayLogo
            //
            this.picBoxEbayLogo.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.picBoxEbayLogo.Image = ((System.Drawing.Image)(resources.GetObject("picBoxEbayLogo.Image")));
            this.picBoxEbayLogo.Location = new System.Drawing.Point(469, 150);
            this.picBoxEbayLogo.Name = "picBoxEbayLogo";
            this.picBoxEbayLogo.Size = new System.Drawing.Size(100, 59);
            this.picBoxEbayLogo.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.picBoxEbayLogo.TabIndex = 26;
            this.picBoxEbayLogo.TabStop = false;
            this.picBoxEbayLogo.Visible = false;
            //
            // webBrowser1
            //
            this.webBrowser1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.webBrowser1.Location = new System.Drawing.Point(0, 0);
            this.webBrowser1.Margin = new System.Windows.Forms.Padding(4);
            this.webBrowser1.MinimumSize = new System.Drawing.Size(25, 25);
            this.webBrowser1.Name = "webBrowser1";
            this.webBrowser1.ScriptErrorsSuppressed = true;
            this.webBrowser1.Size = new System.Drawing.Size(1038, 211);
            this.webBrowser1.TabIndex = 0;
            this.webBrowser1.DocumentCompleted += new System.Windows.Forms.WebBrowserDocumentCompletedEventHandler(this.BrowserDocCompleted);
            //
            // fontDialog1
            //
            this.fontDialog1.Color = System.Drawing.SystemColors.ControlText;
            //
            // openFileDialog1
            //
            this.openFileDialog1.InitialDirectory = "c:\\windows\\media";
            //
            // bgCheckoutWorker
            //
            this.bgCheckoutWorker.WorkerReportsProgress = true;
            this.bgCheckoutWorker.WorkerSupportsCancellation = true;
            //
            // openFileDialog2
            //
            this.openFileDialog2.FileName = "openFileDialog2";
            //
            // notifyIcon1
            //
            this.notifyIcon1.BalloonTipText = "uBuyFirst minimized ";
            this.notifyIcon1.ContextMenuStrip = this.contextMenuStrip1;
            this.notifyIcon1.Icon = ((System.Drawing.Icon)(resources.GetObject("notifyIcon1.Icon")));
            this.notifyIcon1.Text = "uBuyFirst";
            this.notifyIcon1.Visible = true;
            this.notifyIcon1.MouseClick += new System.Windows.Forms.MouseEventHandler(this.TrayIcon_Click);
            //
            // contextMenuStrip1
            //
            this.contextMenuStrip1.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemShow,
            this.toolStripMenuItemExit});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(104, 48);
            //
            // ToolStripMenuItemShow
            //
            this.ToolStripMenuItemShow.Name = "ToolStripMenuItemShow";
            this.ToolStripMenuItemShow.Size = new System.Drawing.Size(103, 22);
            this.ToolStripMenuItemShow.Text = "Show";
            this.ToolStripMenuItemShow.Click += new System.EventHandler(this.toolStripMenuItemShow_Click);
            //
            // toolStripMenuItemExit
            //
            this.toolStripMenuItemExit.Name = "toolStripMenuItemExit";
            this.toolStripMenuItemExit.Size = new System.Drawing.Size(103, 22);
            this.toolStripMenuItemExit.Text = "Exit";
            this.toolStripMenuItemExit.Click += new System.EventHandler(this.toolStripMenuItemExit_Click);
            //
            // fontDialogDGV1
            //
            this.fontDialogDGV1.FontMustExist = true;
            //
            // dockPanel2_Container
            //
            this.dockPanel2_Container.Location = new System.Drawing.Point(4, 21);
            this.dockPanel2_Container.Name = "dockPanel2_Container";
            this.dockPanel2_Container.Size = new System.Drawing.Size(42, 468);
            this.dockPanel2_Container.TabIndex = 0;
            //
            // dockPanel3_Container
            //
            this.dockPanel3_Container.Location = new System.Drawing.Point(4, 21);
            this.dockPanel3_Container.Name = "dockPanel3_Container";
            this.dockPanel3_Container.Size = new System.Drawing.Size(928, 18);
            this.dockPanel3_Container.TabIndex = 0;
            //
            // dockPanel1_Container
            //
            this.dockPanel1_Container.Location = new System.Drawing.Point(4, 28);
            this.dockPanel1_Container.Name = "dockPanel1_Container";
            this.dockPanel1_Container.Size = new System.Drawing.Size(192, 168);
            this.dockPanel1_Container.TabIndex = 0;
            //
            // dockFilters
            //
            this.dockFilters.Controls.Add(this.controlContainer1);
            this.dockFilters.DockedAsTabbedDocument = true;
            this.dockFilters.FloatLocation = new System.Drawing.Point(175, 251);
            this.dockFilters.FloatSize = new System.Drawing.Size(758, 216);
            this.dockFilters.FloatVertical = true;
            this.dockFilters.Header = "Filters";
            this.dockFilters.ID = new System.Guid("42dcb7c0-2f0c-4cc5-9762-35d050eed232");
            this.dockFilters.Name = "dockFilters";
            this.dockFilters.OriginalSize = new System.Drawing.Size(63, 293);
            this.dockFilters.SavedIndex = 0;
            this.dockFilters.SavedMdiDocument = true;
            this.dockFilters.Text = "Filters";
            //
            // controlContainer1
            //
            this.controlContainer1.Controls.Add(this.lstchkXfilterList);
            this.controlContainer1.Controls.Add(this.panelControl2);
            this.controlContainer1.Location = new System.Drawing.Point(0, 0);
            this.controlContainer1.Name = "controlContainer1";
            this.controlContainer1.Size = new System.Drawing.Size(1048, 211);
            this.controlContainer1.TabIndex = 0;
            //
            // panelControl2
            //
            this.panelControl2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.panelControl2.Controls.Add(this.btnAddXfilter);
            this.panelControl2.Controls.Add(this.btnRemoveXfilter);
            this.panelControl2.Controls.Add(this.btnEditXfilter);
            this.panelControl2.Controls.Add(this.btnCopyFilter);
            this.panelControl2.Controls.Add(this.btnSortFilters);
            this.panelControl2.Controls.Add(this.btnExportFilters);
            this.panelControl2.Controls.Add(this.btnImportFilters);
            this.panelControl2.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelControl2.Location = new System.Drawing.Point(0, 0);
            this.panelControl2.Name = "panelControl2";
            this.panelControl2.Size = new System.Drawing.Size(1048, 37);
            this.panelControl2.TabIndex = 104;
            //
            // btnSortFilters
            //
            this.btnSortFilters.ImageOptions.ImageIndex = 11;
            this.btnSortFilters.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnSortFilters.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.SortAscending;
            this.btnSortFilters.Location = new System.Drawing.Point(96, 6);
            this.btnSortFilters.Name = "btnSortFilters";
            this.btnSortFilters.Size = new System.Drawing.Size(25, 25);
            this.btnSortFilters.TabIndex = 3;
            this.btnSortFilters.ToolTip = "Sort filters.";
            this.btnSortFilters.Click += new System.EventHandler(this.BtnSortFilters_Click);
            //
            // btnExportFilters
            //
            this.btnExportFilters.ImageOptions.ImageIndex = 0;
            this.btnExportFilters.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnExportFilters.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Export;
            this.btnExportFilters.ImageOptions.SvgImageSize = new System.Drawing.Size(16, 16);
            this.btnExportFilters.Location = new System.Drawing.Point(136, 6);
            this.btnExportFilters.Name = "btnExportFilters";
            this.btnExportFilters.Size = new System.Drawing.Size(25, 25);
            this.btnExportFilters.TabIndex = 3;
            this.btnExportFilters.ToolTip = "Export filters to CSV file.";
            this.btnExportFilters.Click += new System.EventHandler(this.BtnExportFilters_Click);
            //
            // btnImportFilters
            //
            this.btnImportFilters.ImageOptions.ImageIndex = 11;
            this.btnImportFilters.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnImportFilters.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Import;
            this.btnImportFilters.ImageOptions.SvgImageSize = new System.Drawing.Size(16, 16);
            this.btnImportFilters.Location = new System.Drawing.Point(167, 6);
            this.btnImportFilters.Name = "btnImportFilters";
            this.btnImportFilters.Size = new System.Drawing.Size(25, 25);
            this.btnImportFilters.TabIndex = 3;
            this.btnImportFilters.ToolTip = "Import filters from CSV file.";
            this.btnImportFilters.Click += new System.EventHandler(this.BtnImportFilters_Click);
            //
            // dockSearchQueries
            //
            this.dockSearchQueries.Controls.Add(this.controlContainer2);
            this.dockSearchQueries.DockedAsTabbedDocument = true;
            this.dockSearchQueries.FloatLocation = new System.Drawing.Point(821, 305);
            this.dockSearchQueries.FloatSize = new System.Drawing.Size(758, 216);
            this.dockSearchQueries.ID = new System.Guid("6734d635-9d76-4d86-96c9-a652b982bd36");
            this.dockSearchQueries.Name = "dockSearchQueries";
            this.dockSearchQueries.OriginalSize = new System.Drawing.Size(1038, 141);
            this.dockSearchQueries.SavedIndex = 3;
            this.dockSearchQueries.SavedMdiDocument = true;
            this.dockSearchQueries.Text = "eBay Searches";
            //
            // controlContainer2
            //
            this.controlContainer2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.controlContainer2.Controls.Add(this.splitContainerControl2);
            this.controlContainer2.Controls.Add(this.panelControlKeywordsButtons);
            this.controlContainer2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.controlContainer2.Location = new System.Drawing.Point(0, 0);
            this.controlContainer2.Name = "controlContainer2";
            this.controlContainer2.Size = new System.Drawing.Size(1048, 211);
            this.controlContainer2.TabIndex = 0;
            //
            // splitContainerControl2
            //
            this.splitContainerControl2.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.splitContainerControl2.Appearance.Options.UseBackColor = true;
            this.splitContainerControl2.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl2.Name = "splitContainerControl2";
            //
            // splitContainerControl2.Panel1
            //
            this.splitContainerControl2.Panel1.Controls.Add(this.treeList1);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            //
            // splitContainerControl2.Panel2
            //
            this.splitContainerControl2.Panel2.Controls.Add(this.panelControl1);
            this.splitContainerControl2.Panel2.Controls.Add(this.panelControlDebugInfo);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.PanelVisibility = DevExpress.XtraEditors.SplitPanelVisibility.Panel1;
            this.splitContainerControl2.Size = new System.Drawing.Size(989, 209);
            this.splitContainerControl2.SplitterPosition = 446;
            this.splitContainerControl2.TabIndex = 103;
            this.splitContainerControl2.Text = "splitContainerControl2";
            //
            // treeList1
            //
            this.treeList1.AllowDrop = true;
            this.treeList1.Appearance.FocusedCell.BorderColor = System.Drawing.Color.Black;
            this.treeList1.Appearance.FocusedCell.Options.UseBorderColor = true;
            this.treeList1.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.cEnabled,
            this.cAlias,
            this.cKeywords,
            this.cSearchInDescription,
            this.cPriceMin,
            this.cPriceMax,
            this.cCategoryID,
            this.cCondition,
            this.cSite,
            this.cLocatedIn,
            this.cShipsTo,
            this.cZip,
            this.cSellers,
            this.cSellerType,
            this.cInterval,
            this.cFilter,
            this.cResultWindow,
            this.cListingType,
            this.cPurchasedQuantity,
            this.cRequiredQuantity,
            this.cJobId});
            this.treeList1.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.treeList1.Dock = System.Windows.Forms.DockStyle.Fill;
            treeListFormatRule1.ApplyToRow = true;
            treeListFormatRule1.Column = this.cAlias;
            treeListFormatRule1.ColumnApplyTo = this.cAlias;
            treeListFormatRule1.Name = "Bold Alias Main Search";
            formatConditionRuleExpression1.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            formatConditionRuleExpression1.Appearance.Options.UseFont = true;
            formatConditionRuleExpression1.Expression = "Len([Site]) > 1";
            treeListFormatRule1.Rule = formatConditionRuleExpression1;
            this.treeList1.FormatRules.Add(treeListFormatRule1);
            this.treeList1.Location = new System.Drawing.Point(0, 0);
            this.treeList1.Name = "treeList1";
            this.treeList1.OptionsBehavior.AllowExpandOnDblClick = false;
            this.treeList1.OptionsDragAndDrop.CanCloneNodesOnDrop = true;
            this.treeList1.OptionsDragAndDrop.DragNodesMode = DevExpress.XtraTreeList.DragNodesMode.Single;
            this.treeList1.OptionsFind.ExpandNodesOnIncrementalSearch = true;
            this.treeList1.OptionsLayout.LayoutVersion = "9";
            this.treeList1.OptionsNavigation.AutoFocusNewNode = true;
            this.treeList1.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.treeList1.OptionsSelection.MultiSelect = true;
            this.treeList1.OptionsSelection.UseIndicatorForSelection = true;
            this.treeList1.OptionsView.AllowBandColumnsMultiRow = true;
            this.treeList1.OptionsView.AutoWidth = false;
            this.treeList1.OptionsView.BestFitNodes = DevExpress.XtraTreeList.TreeListBestFitNodes.Visible;
            this.treeList1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemPopupContainerEditFilter,
            this.repositoryItemComboBoxSellerType,
            this.repositoryIntervalEdit,
            this.repositoryItemSpinEditThreads,
            this.repositoryItemDescription,
            this.repositoryItemComboBoxLocatedIn,
            this.repositoryItemComboBoxSite,
            this.repositoryItemComboBoxShipsTo,
            this.repositoryItemEmpty4Filter,
            this.repositoryItemButtonNewKeyword,
            this.repositoryItemEditTextData,
            this.repositoryItemCheckedComboBoxEditCondition,
            this.repositoryItemViews,
            this.repositoryItemCheckedComboBoxEditListingType,
            this.repositoryItemTreeListLookUpEditCategory,
            this.repositoryItemPopupContainerEditCategory,
            this.repositoryItemCheckEditEnabled,
            this.repositoryItemCheckEditFolder,
            this.repositoryItemCheckEditFolderOpen});
            this.treeList1.ShowButtonMode = DevExpress.XtraTreeList.ShowButtonModeEnum.ShowForFocusedRow;
            this.treeList1.Size = new System.Drawing.Size(989, 209);
            this.treeList1.TabIndex = 99;
            this.treeList1.CustomNodeCellEdit += new DevExpress.XtraTreeList.GetCustomNodeCellEditEventHandler(this.treeList1_CustomNodeCellEdit);
            this.treeList1.NodeCellStyle += new DevExpress.XtraTreeList.GetCustomNodeCellStyleEventHandler(this.treeList1_NodeCellStyle);
            this.treeList1.AfterExpand += new DevExpress.XtraTreeList.NodeEventHandler(this.treeList1_AfterExpand);
            this.treeList1.AfterCollapse += new DevExpress.XtraTreeList.NodeEventHandler(this.treeList1_AfterCollapse);
            this.treeList1.AfterCheckNode += new DevExpress.XtraTreeList.NodeEventHandler(this.treeList1_AfterCheckNode);
            this.treeList1.NodeChanged += new DevExpress.XtraTreeList.NodeChangedEventHandler(this.treeList1_NodeChanged);
            this.treeList1.InvalidNodeException += new DevExpress.XtraTreeList.InvalidNodeExceptionEventHandler(this.treeList1_InvalidNodeException);
            this.treeList1.ValidateNode += new DevExpress.XtraTreeList.ValidateNodeEventHandler(this.treeList1_ValidateNode);
            this.treeList1.ShownEditor += new System.EventHandler(this.treeList1_ShownEditor);
            this.treeList1.PopupMenuShowing += new DevExpress.XtraTreeList.PopupMenuShowingEventHandler(this.treeList1_PopupMenuShowing);
            this.treeList1.ShowingEditor += new System.ComponentModel.CancelEventHandler(this.treeList1_ShowingEditor);
            this.treeList1.BeforeLoadLayout += new DevExpress.Utils.LayoutAllowEventHandler(this.treeList1_BeforeLoadLayout);
            this.treeList1.DragDrop += new System.Windows.Forms.DragEventHandler(this.treeList1_DragDrop);
            this.treeList1.DragOver += new System.Windows.Forms.DragEventHandler(this.treeList1_DragOver);
            this.treeList1.Paint += new System.Windows.Forms.PaintEventHandler(this.treeList1_Paint);
            this.treeList1.KeyDown += new System.Windows.Forms.KeyEventHandler(this.treeList1_KeyDown);
            this.treeList1.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeList1_MouseDoubleClick);
            this.treeList1.MouseDown += new System.Windows.Forms.MouseEventHandler(this.treeList1_MouseDown);
            //
            // cEnabled
            //
            this.cEnabled.Caption = "Enabled";
            this.cEnabled.ColumnEdit = this.repositoryItemCheckEditEnabled;
            this.cEnabled.FieldName = "Enabled";
            this.cEnabled.Name = "cEnabled";
            this.cEnabled.ToolTip = "Enable/Disable a Search Term";
            this.cEnabled.UnboundDataType = typeof(bool);
            this.cEnabled.Visible = true;
            this.cEnabled.VisibleIndex = 0;
            //
            // repositoryItemCheckEditEnabled
            //
            this.repositoryItemCheckEditEnabled.AutoHeight = false;
            this.repositoryItemCheckEditEnabled.Name = "repositoryItemCheckEditEnabled";
            this.repositoryItemCheckEditEnabled.EditValueChanged += new System.EventHandler(this.repositoryItemCheckEditEnabled_EditValueChanged);
            //
            // repositoryItemCheckEditFolder
            //
            this.repositoryItemCheckEditFolder.AutoHeight = false;
            this.repositoryItemCheckEditFolder.Name = "repositoryItemCheckEditFolder";
            this.repositoryItemCheckEditFolder.ContextImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Folder;
            this.repositoryItemCheckEditFolder.ContextImageOptions.SvgImageSize = new System.Drawing.Size(16, 16);
            this.repositoryItemCheckEditFolder.EditValueChanged += new System.EventHandler(this.repositoryItemCheckEditEnabled_EditValueChanged);
            //
            // repositoryItemCheckEditFolderOpen
            //
            this.repositoryItemCheckEditFolderOpen.AutoHeight = false;
            this.repositoryItemCheckEditFolderOpen.Name = "repositoryItemCheckEditFolderOpen";
            this.repositoryItemCheckEditFolderOpen.ContextImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.FolderOpen;
            this.repositoryItemCheckEditFolderOpen.ContextImageOptions.SvgImageSize = new System.Drawing.Size(16, 16);
            this.repositoryItemCheckEditFolderOpen.EditValueChanged += new System.EventHandler(this.repositoryItemCheckEditEnabled_EditValueChanged);
            //
            // cKeywords
            //
            this.cKeywords.Caption = "Keywords";
            this.cKeywords.FieldName = "Keywords";
            this.cKeywords.Name = "cKeywords";
            this.cKeywords.ToolTip = resources.GetString("cKeywords.ToolTip");
            this.cKeywords.Visible = true;
            this.cKeywords.VisibleIndex = 2;
            this.cKeywords.Width = 178;
            //
            // cSearchInDescription
            //
            this.cSearchInDescription.Caption = "Search in description";
            this.cSearchInDescription.ColumnEdit = this.repositoryItemDescription;
            this.cSearchInDescription.FieldName = "Search in description";
            this.cSearchInDescription.Name = "cSearchInDescription";
            this.cSearchInDescription.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Boolean;
            this.cSearchInDescription.Visible = true;
            this.cSearchInDescription.VisibleIndex = 3;
            this.cSearchInDescription.Width = 97;
            //
            // repositoryItemDescription
            //
            this.repositoryItemDescription.AutoHeight = false;
            this.repositoryItemDescription.Name = "repositoryItemDescription";
            //
            // cPriceMin
            //
            this.cPriceMin.Caption = "Price Min";
            this.cPriceMin.FieldName = "Price Min";
            this.cPriceMin.Name = "cPriceMin";
            this.cPriceMin.Visible = true;
            this.cPriceMin.VisibleIndex = 4;
            this.cPriceMin.Width = 52;
            //
            // cCategoryID
            //
            this.cCategoryID.Caption = "Category ID";
            this.cCategoryID.ColumnEdit = this.repositoryItemPopupContainerEditCategory;
            this.cCategoryID.FieldName = "Category ID";
            this.cCategoryID.Name = "cCategoryID";
            this.cCategoryID.ToolTip = "Insert eBay category numeric ID. Up to 3 categories. Recommended to enter only on" +
    "e category";
            this.cCategoryID.Visible = true;
            this.cCategoryID.VisibleIndex = 6;
            this.cCategoryID.Width = 100;
            //
            // repositoryItemPopupContainerEditCategory
            //
            this.repositoryItemPopupContainerEditCategory.AutoHeight = false;
            this.repositoryItemPopupContainerEditCategory.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemPopupContainerEditCategory.Name = "repositoryItemPopupContainerEditCategory";
            this.repositoryItemPopupContainerEditCategory.PopupControl = this.popupContainerControlCategory;
            this.repositoryItemPopupContainerEditCategory.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.repositoryItemPopupContainerEditCategory.BeforePopup += new System.EventHandler(this.repositoryItemPopupContainerEditCategory_Popup);
            //
            // popupContainerControlCategory
            //
            this.popupContainerControlCategory.Controls.Add(this.treeListCategory);
            this.popupContainerControlCategory.Location = new System.Drawing.Point(867, 69);
            this.popupContainerControlCategory.Name = "popupContainerControlCategory";
            this.popupContainerControlCategory.Size = new System.Drawing.Size(364, 252);
            this.popupContainerControlCategory.TabIndex = 104;
            //
            // treeListCategory
            //
            this.treeListCategory.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListCategory.KeyFieldName = "CategoryID";
            this.treeListCategory.Location = new System.Drawing.Point(0, 0);
            this.treeListCategory.Name = "treeListCategory";
            this.treeListCategory.OptionsBehavior.Editable = false;
            this.treeListCategory.OptionsCustomization.CustomizationFormSearchBoxVisible = true;
            this.treeListCategory.OptionsFilter.ExpandNodesOnFiltering = true;
            this.treeListCategory.OptionsFilter.FilterMode = DevExpress.XtraTreeList.FilterMode.ParentBranch;
            this.treeListCategory.OptionsFind.AlwaysVisible = true;
            this.treeListCategory.OptionsFind.ClearFindOnClose = false;
            this.treeListCategory.OptionsFind.FindDelay = 500;
            this.treeListCategory.OptionsSelection.MultiSelect = true;
            this.treeListCategory.OptionsView.CheckBoxStyle = DevExpress.XtraTreeList.DefaultNodeCheckBoxStyle.Check;
            this.treeListCategory.OptionsView.FocusRectStyle = DevExpress.XtraTreeList.DrawFocusRectStyle.RowFullFocus;
            this.treeListCategory.OptionsView.ShowIndentAsRowStyle = true;
            this.treeListCategory.Size = new System.Drawing.Size(364, 252);
            this.treeListCategory.TabIndex = 0;
            this.treeListCategory.AfterCheckNode += new DevExpress.XtraTreeList.NodeEventHandler(this.TreeListCategory_AfterCheckNode);
            //
            // cCondition
            //
            this.cCondition.Caption = "Condition";
            this.cCondition.ColumnEdit = this.repositoryItemCheckedComboBoxEditCondition;
            this.cCondition.FieldName = "Condition";
            this.cCondition.FilterMode = DevExpress.XtraGrid.ColumnFilterMode.DisplayText;
            this.cCondition.Name = "cCondition";
            this.cCondition.ToolTip = "Item condition. You may skip this option to receive items with any condition.";
            this.cCondition.Visible = true;
            this.cCondition.VisibleIndex = 7;
            this.cCondition.Width = 86;
            //
            // repositoryItemCheckedComboBoxEditCondition
            //
            this.repositoryItemCheckedComboBoxEditCondition.AutoHeight = false;
            this.repositoryItemCheckedComboBoxEditCondition.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemCheckedComboBoxEditCondition.DropDownRows = 15;
            this.repositoryItemCheckedComboBoxEditCondition.Items.AddRange(new DevExpress.XtraEditors.Controls.CheckedListBoxItem[] {
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("1000", "New"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("1500", "New other"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("1750", "New with defects"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("2000", "Manufacturer refurbished"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("2010", "Excellent Refurbished"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("2020", "Very Good Refurbished"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("2030", "Good Refurbished"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("2500", "Seller refurbished"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("2750", "Like new"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("3000", "Used"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("4000", "Very good"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("5000", "Good"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("6000", "Acceptable"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("7000", "For parts or not working"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("Unspecified", "Unspecified")});
            this.repositoryItemCheckedComboBoxEditCondition.Name = "repositoryItemCheckedComboBoxEditCondition";
            this.repositoryItemCheckedComboBoxEditCondition.NullText = "Any condition";
            this.repositoryItemCheckedComboBoxEditCondition.NullValuePrompt = "ANY";
            this.repositoryItemCheckedComboBoxEditCondition.CustomDisplayText += new DevExpress.XtraEditors.Controls.CustomDisplayTextEventHandler(this.repositoryItemCheckedComboBoxEditCondition_CustomDisplayText);
            //
            // cLocatedIn
            //
            this.cLocatedIn.Caption = "Located in";
            this.cLocatedIn.ColumnEdit = this.repositoryItemComboBoxLocatedIn;
            this.cLocatedIn.FieldName = "Located in";
            this.cLocatedIn.FilterMode = DevExpress.XtraGrid.ColumnFilterMode.DisplayText;
            this.cLocatedIn.Name = "cLocatedIn";
            this.cLocatedIn.ToolTip = "Limits the result set to just those items located in the specified country. This " +
    "is the \'Item Location\'.";
            this.cLocatedIn.Visible = true;
            this.cLocatedIn.VisibleIndex = 9;
            this.cLocatedIn.Width = 60;
            //
            // repositoryItemComboBoxLocatedIn
            //
            this.repositoryItemComboBoxLocatedIn.AutoHeight = false;
            this.repositoryItemComboBoxLocatedIn.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemComboBoxLocatedIn.Name = "repositoryItemComboBoxLocatedIn";
            this.repositoryItemComboBoxLocatedIn.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            //
            // cShipsTo
            //
            this.cShipsTo.Caption = "Ships to";
            this.cShipsTo.ColumnEdit = this.repositoryItemComboBoxShipsTo;
            this.cShipsTo.FieldName = "Ships to";
            this.cShipsTo.FilterMode = DevExpress.XtraGrid.ColumnFilterMode.DisplayText;
            this.cShipsTo.Name = "cShipsTo";
            this.cShipsTo.ToolTip = "Available destination country for a shipping.";
            this.cShipsTo.Visible = true;
            this.cShipsTo.VisibleIndex = 10;
            this.cShipsTo.Width = 70;
            //
            // repositoryItemComboBoxShipsTo
            //
            this.repositoryItemComboBoxShipsTo.AutoHeight = false;
            this.repositoryItemComboBoxShipsTo.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemComboBoxShipsTo.Name = "repositoryItemComboBoxShipsTo";
            this.repositoryItemComboBoxShipsTo.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            //
            // cZip
            //
            this.cZip.Caption = "Ship Zipcode";
            this.cZip.FieldName = "Ship Zipcode";
            this.cZip.FilterMode = DevExpress.XtraGrid.ColumnFilterMode.DisplayText;
            this.cZip.Name = "cZip";
            this.cZip.ToolTip = "Insert your shipping zipcode. Items with calculated shipping will automatically\r\n" +
    "be added to the sale price in the \'Total Price\' field.";
            this.cZip.Visible = true;
            this.cZip.VisibleIndex = 11;
            this.cZip.Width = 70;
            //
            // cSellers
            //
            this.cSellers.Caption = "Sellers";
            this.cSellers.FieldName = "Sellers";
            this.cSellers.FilterMode = DevExpress.XtraGrid.ColumnFilterMode.DisplayText;
            this.cSellers.Name = "cSellers";
            this.cSellers.ToolTip = "Seller names separated by comma. Maximum 10 sellers supported.";
            this.cSellers.Visible = true;
            this.cSellers.VisibleIndex = 12;
            this.cSellers.Width = 46;
            //
            // cSellerType
            //
            this.cSellerType.Caption = "Seller type";
            this.cSellerType.ColumnEdit = this.repositoryItemComboBoxSellerType;
            this.cSellerType.FieldName = "Seller type";
            this.cSellerType.Name = "cSellerType";
            this.cSellerType.ToolTip = "You may include or exclude sellers specified \'Sellers\' column";
            this.cSellerType.Visible = true;
            this.cSellerType.VisibleIndex = 13;
            this.cSellerType.Width = 60;
            //
            // repositoryItemComboBoxSellerType
            //
            this.repositoryItemComboBoxSellerType.AutoHeight = false;
            this.repositoryItemComboBoxSellerType.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemComboBoxSellerType.Items.AddRange(new object[] {
            "",
            "Exclude",
            "Include"});
            this.repositoryItemComboBoxSellerType.Name = "repositoryItemComboBoxSellerType";
            this.repositoryItemComboBoxSellerType.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            //
            // cInterval
            //
            this.cInterval.Caption = "Interval";
            this.cInterval.ColumnEdit = this.repositoryIntervalEdit;
            this.cInterval.FieldName = "Interval";
            this.cInterval.Name = "cInterval";
            this.cInterval.ToolTip = "Search delay.\r\n1 min - perform a search request every 1 minute\r\n30 min - perform " +
    "a search request every 30 minutes, etc.";
            this.cInterval.Visible = true;
            this.cInterval.VisibleIndex = 14;
            this.cInterval.Width = 53;
            //
            // repositoryIntervalEdit
            //
            this.repositoryIntervalEdit.AllowEditDays = false;
            this.repositoryIntervalEdit.AllowEditHours = false;
            this.repositoryIntervalEdit.AutoHeight = false;
            this.repositoryIntervalEdit.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryIntervalEdit.Mask.EditMask = "mm:ss";
            this.repositoryIntervalEdit.MinValue = System.TimeSpan.Parse("00:00:00");
            this.repositoryIntervalEdit.Name = "repositoryIntervalEdit";
            //
            // cFilter
            //
            this.cFilter.AppearanceHeader.Options.UseTextOptions = true;
            this.cFilter.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            this.cFilter.Caption = "Filter";
            this.cFilter.ColumnEdit = this.repositoryItemPopupContainerEditFilter;
            this.cFilter.FieldName = "Filter";
            this.cFilter.Name = "cFilter";
            this.cFilter.ToolTip = "Build a filter to fine tune your searches.";
            this.cFilter.Visible = true;
            this.cFilter.VisibleIndex = 15;
            this.cFilter.Width = 42;
            //
            // repositoryItemPopupContainerEditFilter
            //
            this.repositoryItemPopupContainerEditFilter.AutoHeight = false;
            this.repositoryItemPopupContainerEditFilter.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph)});
            this.repositoryItemPopupContainerEditFilter.Name = "repositoryItemPopupContainerEditFilter";
            this.repositoryItemPopupContainerEditFilter.PopupControl = this.popupContainerControl1;
            this.repositoryItemPopupContainerEditFilter.ShowPopupCloseButton = false;
            this.repositoryItemPopupContainerEditFilter.QueryResultValue += new DevExpress.XtraEditors.Controls.QueryResultValueEventHandler(this.repositoryItemPopupContainerEditFilter_QueryResultValue);
            this.repositoryItemPopupContainerEditFilter.QueryPopUp += new System.ComponentModel.CancelEventHandler(this.repositoryItemPopupContainerEditFilter_QueryPopUp);
            //
            // popupContainerControl1
            //
            this.popupContainerControl1.Controls.Add(this.btnClearFilter);
            this.popupContainerControl1.Controls.Add(this.btnSavePopupFilter);
            this.popupContainerControl1.Controls.Add(this.filterControlTerm);
            this.popupContainerControl1.Controls.Add(this.lblTermName);
            this.popupContainerControl1.Location = new System.Drawing.Point(207, 84);
            this.popupContainerControl1.Margin = new System.Windows.Forms.Padding(2);
            this.popupContainerControl1.MinimumSize = new System.Drawing.Size(200, 100);
            this.popupContainerControl1.Name = "popupContainerControl1";
            this.popupContainerControl1.Size = new System.Drawing.Size(324, 134);
            this.popupContainerControl1.TabIndex = 100;
            //
            // btnClearFilter
            //
            this.btnClearFilter.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnClearFilter.Location = new System.Drawing.Point(4, 108);
            this.btnClearFilter.Margin = new System.Windows.Forms.Padding(2);
            this.btnClearFilter.Name = "btnClearFilter";
            this.btnClearFilter.Size = new System.Drawing.Size(77, 22);
            this.btnClearFilter.TabIndex = 2;
            this.btnClearFilter.Text = "Cancel";
            this.btnClearFilter.Click += new System.EventHandler(this.btnClearFilter_Click);
            //
            // btnSavePopupFilter
            //
            this.btnSavePopupFilter.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSavePopupFilter.Location = new System.Drawing.Point(243, 108);
            this.btnSavePopupFilter.Margin = new System.Windows.Forms.Padding(2);
            this.btnSavePopupFilter.Name = "btnSavePopupFilter";
            this.btnSavePopupFilter.Size = new System.Drawing.Size(77, 22);
            this.btnSavePopupFilter.TabIndex = 2;
            this.btnSavePopupFilter.Text = "Save";
            this.btnSavePopupFilter.Click += new System.EventHandler(this.btnSavePopupFilter_Click);
            //
            // filterControlTerm
            //
            this.filterControlTerm.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.filterControlTerm.AppearanceEmptyValueColor = System.Drawing.Color.Empty;
            this.filterControlTerm.AppearanceFieldNameColor = System.Drawing.Color.Empty;
            this.filterControlTerm.AppearanceGroupOperatorColor = System.Drawing.Color.Empty;
            this.filterControlTerm.AppearanceOperatorColor = System.Drawing.Color.Empty;
            this.filterControlTerm.AppearanceValueColor = System.Drawing.Color.Empty;
            this.filterControlTerm.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.filterControlTerm.LevelIndent = 100;
            this.filterControlTerm.Location = new System.Drawing.Point(2, 2);
            this.filterControlTerm.Margin = new System.Windows.Forms.Padding(2);
            this.filterControlTerm.Name = "filterControlTerm";
            this.filterControlTerm.NodeSeparatorHeight = 2;
            this.filterControlTerm.Size = new System.Drawing.Size(321, 102);
            this.filterControlTerm.TabIndex = 0;
            this.filterControlTerm.UseMenuForOperandsAndOperators = false;
            this.filterControlTerm.FilterChanged += new DevExpress.XtraEditors.FilterChangedEventHandler(this.filterControlTerm_FilterChanged);
            //
            // lblTermName
            //
            this.lblTermName.Location = new System.Drawing.Point(4, 4);
            this.lblTermName.Margin = new System.Windows.Forms.Padding(2);
            this.lblTermName.Name = "lblTermName";
            this.lblTermName.Size = new System.Drawing.Size(28, 13);
            this.lblTermName.TabIndex = 1;
            this.lblTermName.Text = "Term:";
            //
            // cResultWindow
            //
            this.cResultWindow.Caption = "View";
            this.cResultWindow.ColumnEdit = this.repositoryItemViews;
            this.cResultWindow.FieldName = "View";
            this.cResultWindow.Name = "cResultWindow";
            this.cResultWindow.ToolTip = "You can create up to 10 Views. Each View can display results for several eBay sea" +
    "rches. Example: One View shows results for Clothes, another View - for phones";
            this.cResultWindow.Visible = true;
            this.cResultWindow.VisibleIndex = 16;
            //
            // repositoryItemViews
            //
            this.repositoryItemViews.ActionButtonIndex = 2;
            this.repositoryItemViews.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.repositoryItemViews.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Plus, "Create View", -1, true, true, false, editorButtonImageOptions3, new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject9, serializableAppearanceObject10, serializableAppearanceObject11, serializableAppearanceObject12, "Create View", null, null, DevExpress.Utils.ToolTipAnchor.Default),
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Close, "Cancel", -1, true, false, false, editorButtonImageOptions4, new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject13, serializableAppearanceObject14, serializableAppearanceObject15, serializableAppearanceObject16, "Cancel", null, null, DevExpress.Utils.ToolTipAnchor.Default),
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemViews.CloseUpKey = new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.F2);
            this.repositoryItemViews.Items.AddRange(new object[] {
            "Results"});
            this.repositoryItemViews.MaxItemCount = 10;
            this.repositoryItemViews.Name = "repositoryItemViews";
            this.repositoryItemViews.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.repositoryItemViews.AddingMRUItem += new DevExpress.XtraEditors.Controls.AddingMRUItemEventHandler(this.repositoryItemViews_AddingMRUItem);
            this.repositoryItemViews.RemovingMRUItem += new DevExpress.XtraEditors.Controls.RemovingMRUItemEventHandler(this.repositoryItemViews_RemovingMRUItem);
            //
            // cListingType
            //
            this.cListingType.Caption = "Type";
            this.cListingType.ColumnEdit = this.repositoryItemCheckedComboBoxEditListingType;
            this.cListingType.FieldName = "ListingType";
            this.cListingType.FilterMode = DevExpress.XtraGrid.ColumnFilterMode.DisplayText;
            this.cListingType.Name = "cListingType";
            this.cListingType.ToolTip = "EBay listing type";
            this.cListingType.Visible = true;
            this.cListingType.VisibleIndex = 17;
            this.cListingType.Width = 50;
            //
            // repositoryItemCheckedComboBoxEditListingType
            //
            this.repositoryItemCheckedComboBoxEditListingType.AutoHeight = false;
            this.repositoryItemCheckedComboBoxEditListingType.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemCheckedComboBoxEditListingType.Items.AddRange(new DevExpress.XtraEditors.Controls.CheckedListBoxItem[] {
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("BuyItNow", "Buy It Now"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("AuctionsStartedNow", "Auctions Started Now"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("AuctionsEndingNow", "Auctions Ending Now"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem("OutOfStock", "Out Of Stock")});
            this.repositoryItemCheckedComboBoxEditListingType.Name = "repositoryItemCheckedComboBoxEditListingType";
            this.repositoryItemCheckedComboBoxEditListingType.EditValueChanged += new System.EventHandler(this.RepositoryItemCheckedComboBoxEditListingType_EditValueChanged);
            this.repositoryItemCheckedComboBoxEditListingType.CustomDisplayText += new DevExpress.XtraEditors.Controls.CustomDisplayTextEventHandler(this.repositoryItemCheckedComboBoxEditListingType_CustomDisplayText);
            //
            // cPurchasedQuantity
            //
            this.cPurchasedQuantity.Caption = "PurchasedQuantity";
            this.cPurchasedQuantity.FieldName = "PurchasedQuantity";
            this.cPurchasedQuantity.Name = "cPurchasedQuantity";
            this.cPurchasedQuantity.OptionsColumn.AllowEdit = false;
            this.cPurchasedQuantity.OptionsColumn.ReadOnly = true;
            this.cPurchasedQuantity.ToolTip = "Purchased quantity for AutoPurchase system";
            this.cPurchasedQuantity.Visible = true;
            this.cPurchasedQuantity.VisibleIndex = 18;
            this.cPurchasedQuantity.Width = 100;
            //
            // cRequiredQuantity
            //
            this.cRequiredQuantity.Caption = "RequiredQuantity";
            this.cRequiredQuantity.FieldName = "RequiredQuantity";
            this.cRequiredQuantity.Name = "cRequiredQuantity";
            this.cRequiredQuantity.ToolTip = "Required quantity for AutoPurchase system";
            this.cRequiredQuantity.Visible = true;
            this.cRequiredQuantity.VisibleIndex = 19;
            this.cRequiredQuantity.Width = 100;
            //
            // cJobId
            //
            this.cJobId.Caption = "JobId";
            this.cJobId.FieldName = "JobId";
            this.cJobId.Name = "cJobId";
            this.cJobId.ToolTip = "Job identifier for AutoPurchase system";
            this.cJobId.Visible = true;
            this.cJobId.VisibleIndex = 20;
            this.cJobId.Width = 80;
            //
            // repositoryItemSpinEditThreads
            //
            this.repositoryItemSpinEditThreads.AutoHeight = false;
            this.repositoryItemSpinEditThreads.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemSpinEditThreads.IsFloatValue = false;
            this.repositoryItemSpinEditThreads.Mask.EditMask = "N00";
            this.repositoryItemSpinEditThreads.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.repositoryItemSpinEditThreads.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.repositoryItemSpinEditThreads.Name = "repositoryItemSpinEditThreads";
            //
            // repositoryItemEmpty4Filter
            //
            this.repositoryItemEmpty4Filter.AutoHeight = false;
            this.repositoryItemEmpty4Filter.Name = "repositoryItemEmpty4Filter";
            this.repositoryItemEmpty4Filter.ReadOnly = true;
            this.repositoryItemEmpty4Filter.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor;
            //
            // repositoryItemEditTextData
            //
            this.repositoryItemEditTextData.AutoHeight = false;
            this.repositoryItemEditTextData.Name = "repositoryItemEditTextData";
            //
            // repositoryItemTreeListLookUpEditCategory
            //
            this.repositoryItemTreeListLookUpEditCategory.AutoHeight = false;
            this.repositoryItemTreeListLookUpEditCategory.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemTreeListLookUpEditCategory.Name = "repositoryItemTreeListLookUpEditCategory";
            this.repositoryItemTreeListLookUpEditCategory.NullText = "Null";
            this.repositoryItemTreeListLookUpEditCategory.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.repositoryItemTreeListLookUpEditCategory.TreeList = this.repositoryItemTreeListLookUpEdit1TreeList;
            this.repositoryItemTreeListLookUpEditCategory.BeforePopup += new System.EventHandler(this.repositoryItemTreeListLookUpEditCategory_BeforePopup);
            this.repositoryItemTreeListLookUpEditCategory.CustomDisplayText += new DevExpress.XtraEditors.Controls.CustomDisplayTextEventHandler(this.repositoryItemTreeListLookUpEditCategory_CustomDisplayText);
            //
            // repositoryItemTreeListLookUpEdit1TreeList
            //
            this.repositoryItemTreeListLookUpEdit1TreeList.Location = new System.Drawing.Point(0, 0);
            this.repositoryItemTreeListLookUpEdit1TreeList.Name = "repositoryItemTreeListLookUpEdit1TreeList";
            this.repositoryItemTreeListLookUpEdit1TreeList.OptionsCustomization.CustomizationFormSearchBoxVisible = true;
            this.repositoryItemTreeListLookUpEdit1TreeList.OptionsFilter.ExpandNodesOnFiltering = true;
            this.repositoryItemTreeListLookUpEdit1TreeList.OptionsFilter.FilterMode = DevExpress.XtraTreeList.FilterMode.ParentBranch;
            this.repositoryItemTreeListLookUpEdit1TreeList.OptionsFind.AllowFindPanel = true;
            this.repositoryItemTreeListLookUpEdit1TreeList.OptionsFind.AlwaysVisible = true;
            this.repositoryItemTreeListLookUpEdit1TreeList.OptionsFind.ClearFindOnClose = false;
            this.repositoryItemTreeListLookUpEdit1TreeList.OptionsFind.FindDelay = 500;
            this.repositoryItemTreeListLookUpEdit1TreeList.OptionsSelection.MultiSelect = true;
            this.repositoryItemTreeListLookUpEdit1TreeList.OptionsView.CheckBoxStyle = DevExpress.XtraTreeList.DefaultNodeCheckBoxStyle.Check;
            this.repositoryItemTreeListLookUpEdit1TreeList.OptionsView.FocusRectStyle = DevExpress.XtraTreeList.DrawFocusRectStyle.RowFullFocus;
            this.repositoryItemTreeListLookUpEdit1TreeList.OptionsView.ShowIndentAsRowStyle = true;
            this.repositoryItemTreeListLookUpEdit1TreeList.Size = new System.Drawing.Size(400, 200);
            this.repositoryItemTreeListLookUpEdit1TreeList.TabIndex = 0;
            //
            // panelControl1
            //
            this.panelControl1.Controls.Add(this.checkUseAPI);
            this.panelControl1.Controls.Add(this.checkEditShowSoldItems);
            this.panelControl1.Controls.Add(this.checkUseRSS);
            this.panelControl1.Controls.Add(this.checkUseRSS2);
            this.panelControl1.Controls.Add(this.lblFindReqMaxThreads);
            this.panelControl1.Controls.Add(this.checkEditWhitespaceTokenizer);
            this.panelControl1.Controls.Add(this.spinEditFindReqMaxThreads);
            this.panelControl1.Controls.Add(this.btnDebugStatsInfo);
            this.panelControl1.Controls.Add(this.spinEditGetItemDetailsMaxThreads);
            this.panelControl1.Controls.Add(this.chkUpdateItemStatusFor2Min);
            this.panelControl1.Controls.Add(this.lblGetItemDetailsMaxThreads);
            this.panelControl1.Controls.Add(this.chkDownloadAvatars);
            this.panelControl1.Controls.Add(this.chkDownloadOtherImages);
            this.panelControl1.Controls.Add(this.chkDownloadDescription);
            this.panelControl1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelControl1.Location = new System.Drawing.Point(0, -133);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(0, 133);
            this.panelControl1.TabIndex = 2;
            //
            // checkUseAPI
            //
            this.checkUseAPI.Location = new System.Drawing.Point(317, 57);
            this.checkUseAPI.Name = "checkUseAPI";
            this.checkUseAPI.Properties.Caption = "Use API";
            this.checkUseAPI.Size = new System.Drawing.Size(74, 20);
            this.checkUseAPI.TabIndex = 3;
            this.checkUseAPI.CheckedChanged += new System.EventHandler(this._requestCfgChanged);
            //
            // checkEditShowSoldItems
            //
            this.checkEditShowSoldItems.Location = new System.Drawing.Point(5, 106);
            this.checkEditShowSoldItems.Name = "checkEditShowSoldItems";
            this.checkEditShowSoldItems.Properties.Caption = "Show sold items";
            this.checkEditShowSoldItems.Size = new System.Drawing.Size(117, 20);
            this.checkEditShowSoldItems.TabIndex = 3;
            this.checkEditShowSoldItems.CheckedChanged += new System.EventHandler(this._requestCfgChanged);
            //
            // checkUseRSS
            //
            this.checkUseRSS.Location = new System.Drawing.Point(317, 84);
            this.checkUseRSS.Name = "checkUseRSS";
            this.checkUseRSS.Properties.Caption = "Use RSS";
            this.checkUseRSS.Size = new System.Drawing.Size(74, 20);
            this.checkUseRSS.TabIndex = 3;
            this.checkUseRSS.CheckedChanged += new System.EventHandler(this._requestCfgChanged);
            //
            // checkUseRSS2
            //
            this.checkUseRSS2.Location = new System.Drawing.Point(317, 106);
            this.checkUseRSS2.Name = "checkUseRSS2";
            this.checkUseRSS2.Properties.Caption = "Use RSS2";
            this.checkUseRSS2.Size = new System.Drawing.Size(74, 20);
            this.checkUseRSS2.TabIndex = 3;
            this.checkUseRSS2.CheckedChanged += new System.EventHandler(this._requestCfgChanged);
            //
            // lblFindReqMaxThreads
            //
            this.lblFindReqMaxThreads.Location = new System.Drawing.Point(4, 10);
            this.lblFindReqMaxThreads.Name = "lblFindReqMaxThreads";
            this.lblFindReqMaxThreads.Size = new System.Drawing.Size(107, 13);
            this.lblFindReqMaxThreads.TabIndex = 2;
            this.lblFindReqMaxThreads.Text = "Find Req Max Threads";
            //
            // checkEditWhitespaceTokenizer
            //
            this.checkEditWhitespaceTokenizer.Location = new System.Drawing.Point(155, 106);
            this.checkEditWhitespaceTokenizer.MenuManager = this.ribbonControl1;
            this.checkEditWhitespaceTokenizer.Name = "checkEditWhitespaceTokenizer";
            this.checkEditWhitespaceTokenizer.Properties.Caption = "Whitespace splitter";
            this.checkEditWhitespaceTokenizer.Size = new System.Drawing.Size(128, 20);
            this.checkEditWhitespaceTokenizer.TabIndex = 5;
            this.checkEditWhitespaceTokenizer.ToolTipTitle = "Splite words on whitespaces (subsearches)";
            this.checkEditWhitespaceTokenizer.CheckedChanged += new System.EventHandler(this._requestCfgChanged);
            //
            // spinEditFindReqMaxThreads
            //
            this.spinEditFindReqMaxThreads.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.spinEditFindReqMaxThreads.Location = new System.Drawing.Point(155, 7);
            this.spinEditFindReqMaxThreads.MenuManager = this.ribbonControl1;
            this.spinEditFindReqMaxThreads.Name = "spinEditFindReqMaxThreads";
            this.spinEditFindReqMaxThreads.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.spinEditFindReqMaxThreads.Properties.IsFloatValue = false;
            this.spinEditFindReqMaxThreads.Properties.Mask.EditMask = "N00";
            this.spinEditFindReqMaxThreads.Properties.MaxValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.spinEditFindReqMaxThreads.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.spinEditFindReqMaxThreads.Size = new System.Drawing.Size(47, 20);
            this.spinEditFindReqMaxThreads.TabIndex = 1;
            this.spinEditFindReqMaxThreads.EditValueChanged += new System.EventHandler(this._requestCfgChanged);
            //
            // btnDebugStatsInfo
            //
            this.btnDebugStatsInfo.Location = new System.Drawing.Point(223, 5);
            this.btnDebugStatsInfo.Name = "btnDebugStatsInfo";
            this.btnDebugStatsInfo.Size = new System.Drawing.Size(75, 23);
            this.btnDebugStatsInfo.TabIndex = 4;
            this.btnDebugStatsInfo.Text = "Info";
            this.btnDebugStatsInfo.Click += new System.EventHandler(this.Info_Click);
            //
            // spinEditGetItemDetailsMaxThreads
            //
            this.spinEditGetItemDetailsMaxThreads.EditValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.spinEditGetItemDetailsMaxThreads.Location = new System.Drawing.Point(155, 33);
            this.spinEditGetItemDetailsMaxThreads.Name = "spinEditGetItemDetailsMaxThreads";
            this.spinEditGetItemDetailsMaxThreads.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.spinEditGetItemDetailsMaxThreads.Properties.IsFloatValue = false;
            this.spinEditGetItemDetailsMaxThreads.Properties.Mask.EditMask = "N00";
            this.spinEditGetItemDetailsMaxThreads.Properties.MaxValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.spinEditGetItemDetailsMaxThreads.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.spinEditGetItemDetailsMaxThreads.Size = new System.Drawing.Size(47, 20);
            this.spinEditGetItemDetailsMaxThreads.TabIndex = 1;
            this.spinEditGetItemDetailsMaxThreads.EditValueChanged += new System.EventHandler(this._requestCfgChanged);
            //
            // chkUpdateItemStatusFor2Min
            //
            this.chkUpdateItemStatusFor2Min.Location = new System.Drawing.Point(155, 84);
            this.chkUpdateItemStatusFor2Min.Name = "chkUpdateItemStatusFor2Min";
            this.chkUpdateItemStatusFor2Min.Properties.Caption = "Update Item Status For 2 Min";
            this.chkUpdateItemStatusFor2Min.Size = new System.Drawing.Size(163, 20);
            this.chkUpdateItemStatusFor2Min.TabIndex = 3;
            this.chkUpdateItemStatusFor2Min.CheckedChanged += new System.EventHandler(this._requestCfgChanged);
            //
            // lblGetItemDetailsMaxThreads
            //
            this.lblGetItemDetailsMaxThreads.Location = new System.Drawing.Point(4, 37);
            this.lblGetItemDetailsMaxThreads.Name = "lblGetItemDetailsMaxThreads";
            this.lblGetItemDetailsMaxThreads.Size = new System.Drawing.Size(142, 13);
            this.lblGetItemDetailsMaxThreads.TabIndex = 2;
            this.lblGetItemDetailsMaxThreads.Text = "Get Item Details Max Threads";
            //
            // chkDownloadAvatars
            //
            this.chkDownloadAvatars.Location = new System.Drawing.Point(5, 55);
            this.chkDownloadAvatars.MenuManager = this.ribbonControl1;
            this.chkDownloadAvatars.Name = "chkDownloadAvatars";
            this.chkDownloadAvatars.Properties.Caption = "Download Avatars";
            this.chkDownloadAvatars.Size = new System.Drawing.Size(142, 20);
            this.chkDownloadAvatars.TabIndex = 3;
            this.chkDownloadAvatars.CheckedChanged += new System.EventHandler(this._requestCfgChanged);
            //
            // chkDownloadOtherImages
            //
            this.chkDownloadOtherImages.Location = new System.Drawing.Point(5, 80);
            this.chkDownloadOtherImages.Name = "chkDownloadOtherImages";
            this.chkDownloadOtherImages.Properties.Caption = "Download Other Images";
            this.chkDownloadOtherImages.Size = new System.Drawing.Size(142, 20);
            this.chkDownloadOtherImages.TabIndex = 3;
            this.chkDownloadOtherImages.CheckedChanged += new System.EventHandler(this._requestCfgChanged);
            //
            // chkDownloadDescription
            //
            this.chkDownloadDescription.Location = new System.Drawing.Point(155, 58);
            this.chkDownloadDescription.Name = "chkDownloadDescription";
            this.chkDownloadDescription.Properties.Caption = "Download Description";
            this.chkDownloadDescription.Size = new System.Drawing.Size(163, 20);
            this.chkDownloadDescription.TabIndex = 3;
            this.chkDownloadDescription.CheckedChanged += new System.EventHandler(this._requestCfgChanged);
            //
            // panelControlDebugInfo
            //
            this.panelControlDebugInfo.Controls.Add(this.txtDebugInfo1);
            this.panelControlDebugInfo.Controls.Add(this.popupContainerControl1);
            this.panelControlDebugInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelControlDebugInfo.Location = new System.Drawing.Point(0, 0);
            this.panelControlDebugInfo.Name = "panelControlDebugInfo";
            this.panelControlDebugInfo.Size = new System.Drawing.Size(0, 0);
            this.panelControlDebugInfo.TabIndex = 1;
            //
            // txtDebugInfo1
            //
            this.txtDebugInfo1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.txtDebugInfo1.Location = new System.Drawing.Point(0, 0);
            this.txtDebugInfo1.Name = "txtDebugInfo1";
            this.txtDebugInfo1.Size = new System.Drawing.Size(0, 0);
            this.txtDebugInfo1.TabIndex = 0;
            this.txtDebugInfo1.Text = "";
            //
            // panelControlKeywordsButtons
            //
            this.panelControlKeywordsButtons.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.panelControlKeywordsButtons.Controls.Add(this.btnImportSearches);
            this.panelControlKeywordsButtons.Controls.Add(this.btnCreateFolder);
            this.panelControlKeywordsButtons.Controls.Add(this.btnNewSearchQuery);
            this.panelControlKeywordsButtons.Controls.Add(this.btnExportSearches);
            this.panelControlKeywordsButtons.Controls.Add(this.btnNewChildTerm);
            this.panelControlKeywordsButtons.Controls.Add(this.btnRemoveSearch);
            this.panelControlKeywordsButtons.Dock = System.Windows.Forms.DockStyle.Right;
            this.panelControlKeywordsButtons.Location = new System.Drawing.Point(989, 0);
            this.panelControlKeywordsButtons.Name = "panelControlKeywordsButtons";
            this.panelControlKeywordsButtons.Size = new System.Drawing.Size(57, 209);
            this.panelControlKeywordsButtons.TabIndex = 2;
            //
            // btnImportSearches
            //
            this.btnImportSearches.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnImportSearches.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Import;
            this.btnImportSearches.ImageOptions.SvgImageSize = new System.Drawing.Size(10, 10);
            this.btnImportSearches.Location = new System.Drawing.Point(8, 163);
            this.btnImportSearches.Name = "btnImportSearches";
            this.btnImportSearches.ShowFocusRectangle = DevExpress.Utils.DefaultBoolean.False;
            this.btnImportSearches.Size = new System.Drawing.Size(20, 30);
            this.btnImportSearches.TabIndex = 103;
            this.btnImportSearches.ToolTip = "Import searches from CSV.\r\nTo get a CSV template for import - first create few ke" +
    "ywords and export them. Exported file will be your import template.\r\n";
            this.btnImportSearches.Click += new System.EventHandler(this.btnImportKeywords_Click);
            //
            // btnCreateFolder
            //
            this.btnCreateFolder.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnCreateFolder.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("btnCreateFolder.ImageOptions.SvgImage")));
            this.btnCreateFolder.ImageOptions.SvgImageSize = new System.Drawing.Size(17, 17);
            this.btnCreateFolder.Location = new System.Drawing.Point(14, 21);
            this.btnCreateFolder.Name = "btnCreateFolder";
            this.btnCreateFolder.Size = new System.Drawing.Size(30, 30);
            this.btnCreateFolder.TabIndex = 100;
            this.btnCreateFolder.ToolTip = "Create Folder for search terms";
            this.btnCreateFolder.Click += new System.EventHandler(this.btnCreateFolder_Click);
            //
            // btnNewSearchQuery
            //
            this.btnNewSearchQuery.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnNewSearchQuery.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Add;
            this.btnNewSearchQuery.ImageOptions.SvgImageSize = new System.Drawing.Size(17, 17);
            this.btnNewSearchQuery.Location = new System.Drawing.Point(14, 57);
            this.btnNewSearchQuery.Name = "btnNewSearchQuery";
            this.btnNewSearchQuery.Size = new System.Drawing.Size(30, 30);
            this.btnNewSearchQuery.TabIndex = 100;
            this.btnNewSearchQuery.ToolTip = "Create new eBay Search [Insert]";
            this.btnNewSearchQuery.Click += new System.EventHandler(this.btnNewSearchQuery_Click);
            //
            // btnExportSearches
            //
            this.btnExportSearches.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnExportSearches.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Export;
            this.btnExportSearches.ImageOptions.SvgImageSize = new System.Drawing.Size(10, 10);
            this.btnExportSearches.Location = new System.Drawing.Point(29, 163);
            this.btnExportSearches.Name = "btnExportSearches";
            this.btnExportSearches.Size = new System.Drawing.Size(20, 30);
            this.btnExportSearches.TabIndex = 104;
            this.btnExportSearches.ToolTip = "Export searches to CSV";
            this.btnExportSearches.Click += new System.EventHandler(this.btnExportSearches_Click);
            //
            // btnNewChildTerm
            //
            this.btnNewChildTerm.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnNewChildTerm.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.CreateSubsearch;
            this.btnNewChildTerm.ImageOptions.SvgImageSize = new System.Drawing.Size(10, 10);
            this.btnNewChildTerm.Location = new System.Drawing.Point(14, 92);
            this.btnNewChildTerm.Name = "btnNewChildTerm";
            this.btnNewChildTerm.Size = new System.Drawing.Size(30, 30);
            this.btnNewChildTerm.TabIndex = 101;
            this.btnNewChildTerm.ToolTip = "New Sub Search";
            this.btnNewChildTerm.Click += new System.EventHandler(this.btnNewChildTerm_Click);
            //
            // btnRemoveSearch
            //
            this.btnRemoveSearch.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnRemoveSearch.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Remove;
            this.btnRemoveSearch.ImageOptions.SvgImageSize = new System.Drawing.Size(10, 10);
            this.btnRemoveSearch.Location = new System.Drawing.Point(14, 127);
            this.btnRemoveSearch.Name = "btnRemoveSearch";
            this.btnRemoveSearch.Size = new System.Drawing.Size(30, 30);
            this.btnRemoveSearch.TabIndex = 102;
            this.btnRemoveSearch.ToolTip = "Delete selected searches";
            this.btnRemoveSearch.Click += new System.EventHandler(this.btnRemoveSearch_Click);
            //
            // dockItemProperties
            //
            this.dockItemProperties.Controls.Add(this.controlContainer5);
            this.dockItemProperties.DockedAsTabbedDocument = true;
            this.dockItemProperties.FloatLocation = new System.Drawing.Point(906, 422);
            this.dockItemProperties.FloatSize = new System.Drawing.Size(758, 216);
            this.dockItemProperties.FloatVertical = true;
            this.dockItemProperties.Header = "Item Properties";
            this.dockItemProperties.ID = new System.Guid("fb83c491-ecb3-4c39-85ce-de001cd56310");
            this.dockItemProperties.Name = "dockItemProperties";
            this.dockItemProperties.OriginalSize = new System.Drawing.Size(506, 210);
            this.dockItemProperties.SavedIndex = 6;
            this.dockItemProperties.SavedMdiDocument = true;
            this.dockItemProperties.Text = "Item Properties";
            this.dockItemProperties.CustomButtonClick += new DevExpress.XtraBars.Docking2010.ButtonEventHandler(this.dockItemProperties_CustomButtonClick);
            //
            // controlContainer5
            //
            this.controlContainer5.Controls.Add(this.layoutControl1);
            this.controlContainer5.Location = new System.Drawing.Point(0, 0);
            this.controlContainer5.Name = "controlContainer5";
            this.controlContainer5.Size = new System.Drawing.Size(1048, 211);
            this.controlContainer5.TabIndex = 0;
            //
            // layoutControl1
            //
            this.layoutControl1.Appearance.Control.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.layoutControl1.Appearance.Control.Options.UseFont = true;
            this.layoutControl1.Controls.Add(this.lcShippingDays);
            this.layoutControl1.Controls.Add(this.lcBids);
            this.layoutControl1.Controls.Add(this.lcListingType);
            this.layoutControl1.Controls.Add(this.lcAuctionPrice);
            this.layoutControl1.Controls.Add(this.lcPayment);
            this.layoutControl1.Controls.Add(this.btnEditItemProperties);
            this.layoutControl1.Controls.Add(this.lcEbayAccount);
            this.layoutControl1.Controls.Add(this.lcItemID);
            this.layoutControl1.Controls.Add(this.lcTerm);
            this.layoutControl1.Controls.Add(this.lcTitle);
            this.layoutControl1.Controls.Add(this.lcCondition);
            this.layoutControl1.Controls.Add(this.lcReturns);
            this.layoutControl1.Controls.Add(this.lcBestOffer);
            this.layoutControl1.Controls.Add(this.lcFoundTime);
            this.layoutControl1.Controls.Add(this.lcLocation);
            this.layoutControl1.Controls.Add(this.lcFromCountry);
            this.layoutControl1.Controls.Add(this.lcToCountry);
            this.layoutControl1.Controls.Add(this.lcAutoPay);
            this.layoutControl1.Controls.Add(this.lcCategoryID);
            this.layoutControl1.Controls.Add(this.lcCategoryName);
            this.layoutControl1.Controls.Add(this.lcConditionDescription);
            this.layoutControl1.Controls.Add(this.lcFeedbackRating);
            this.layoutControl1.Controls.Add(this.lcFeedbackScore);
            this.layoutControl1.Controls.Add(this.lcItemPrice);
            this.layoutControl1.Controls.Add(this.lcPostedTime);
            this.layoutControl1.Controls.Add(this.lcQuantity);
            this.layoutControl1.Controls.Add(this.lcSellerName);
            this.layoutControl1.Controls.Add(this.lcShipping);
            this.layoutControl1.Controls.Add(this.lcShippingType);
            this.layoutControl1.Controls.Add(this.lcShipAdditionalItem);
            this.layoutControl1.Controls.Add(this.lcSoldTime);
            this.layoutControl1.Controls.Add(this.lcEbayWebsite);
            this.layoutControl1.Controls.Add(this.lcPageViews);
            this.layoutControl1.Controls.Add(this.lcUPC);
            this.layoutControl1.Controls.Add(this.lcVariation);
            this.layoutControl1.Controls.Add(this.lcTotalPrice);
            this.layoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutControl1.HiddenItems.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.lciVariation,
            this.lciUPC,
            this.lciTerm,
            this.lciSoldTime,
            this.lciShipAdditionalItem,
            this.lciShippingType,
            this.lciPageViews,
            this.lciPostedTime,
            this.lciFoundTime,
            this.lciEbayWebsite,
            this.lciEbayAccount,
            this.lciCategoryName,
            this.lciCategoryID,
            this.lciBestOffer,
            this.lciAutoPay,
            this.lciItemPrice,
            this.lciShipping,
            this.lciToCountry,
            this.lciFromCountry,
            this.lciPayment,
            this.lciShippingDays});
            this.layoutControl1.LayoutVersion = "2";
            this.layoutControl1.Location = new System.Drawing.Point(0, 0);
            this.layoutControl1.Name = "layoutControl1";
            this.layoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = new System.Drawing.Rectangle(762, 112, 1158, 968);
            this.layoutControl1.OptionsCustomizationForm.ShowPropertyGrid = true;
            this.layoutControl1.OptionsItemText.TextAlignMode = DevExpress.XtraLayout.TextAlignMode.CustomSize;
            this.layoutControl1.OptionsSerialization.RestoreAppearanceItemCaption = true;
            this.layoutControl1.OptionsSerialization.RestoreAppearanceTabPage = true;
            this.layoutControl1.OptionsSerialization.RestoreGroupPadding = true;
            this.layoutControl1.OptionsSerialization.RestoreGroupSpacing = true;
            this.layoutControl1.OptionsSerialization.RestoreLayoutGroupAppearanceGroup = true;
            this.layoutControl1.OptionsSerialization.RestoreLayoutItemPadding = true;
            this.layoutControl1.OptionsSerialization.RestoreLayoutItemSpacing = true;
            this.layoutControl1.OptionsSerialization.RestoreLayoutItemText = false;
            this.layoutControl1.OptionsSerialization.RestoreRootGroupPadding = true;
            this.layoutControl1.OptionsSerialization.RestoreRootGroupSpacing = true;
            this.layoutControl1.OptionsSerialization.RestoreTabbedGroupPadding = true;
            this.layoutControl1.OptionsSerialization.RestoreTabbedGroupSpacing = true;
            this.layoutControl1.OptionsSerialization.RestoreTextToControlDistance = true;
            this.layoutControl1.Root = this.layoutControlGroup1;
            this.layoutControl1.Size = new System.Drawing.Size(1048, 211);
            this.layoutControl1.TabIndex = 1;
            this.layoutControl1.Text = "layoutControl1";
            //
            // lcShippingDays
            //
            this.lcShippingDays.Location = new System.Drawing.Point(116, 183);
            this.lcShippingDays.Name = "lcShippingDays";
            this.lcShippingDays.Size = new System.Drawing.Size(0, 13);
            this.lcShippingDays.StyleController = this.layoutControl1;
            this.lcShippingDays.TabIndex = 1;
            //
            // lcBids
            //
            this.lcBids.Location = new System.Drawing.Point(74, 198);
            this.lcBids.Name = "lcBids";
            this.lcBids.Size = new System.Drawing.Size(945, 20);
            this.lcBids.StyleController = this.layoutControl1;
            this.lcBids.TabIndex = 1;
            //
            // lcListingType
            //
            this.lcListingType.Location = new System.Drawing.Point(74, 174);
            this.lcListingType.Name = "lcListingType";
            this.lcListingType.Size = new System.Drawing.Size(945, 20);
            this.lcListingType.StyleController = this.layoutControl1;
            this.lcListingType.TabIndex = 1;
            //
            // lcAuctionPrice
            //
            this.lcAuctionPrice.Location = new System.Drawing.Point(74, 150);
            this.lcAuctionPrice.Name = "lcAuctionPrice";
            this.lcAuctionPrice.Size = new System.Drawing.Size(945, 20);
            this.lcAuctionPrice.StyleController = this.layoutControl1;
            this.lcAuctionPrice.TabIndex = 1;
            //
            // lcPayment
            //
            this.lcPayment.Location = new System.Drawing.Point(116, 105);
            this.lcPayment.Name = "lcPayment";
            this.lcPayment.Size = new System.Drawing.Size(0, 13);
            this.lcPayment.StyleController = this.layoutControl1;
            this.lcPayment.TabIndex = 1;
            //
            // btnEditItemProperties
            //
            this.btnEditItemProperties.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnEditItemProperties.ImageOptions.Image")));
            this.btnEditItemProperties.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnEditItemProperties.Location = new System.Drawing.Point(1006, 12);
            this.btnEditItemProperties.MaximumSize = new System.Drawing.Size(20, 20);
            this.btnEditItemProperties.Name = "btnEditItemProperties";
            this.btnEditItemProperties.Size = new System.Drawing.Size(13, 13);
            this.btnEditItemProperties.StyleController = this.layoutControl1;
            this.btnEditItemProperties.TabIndex = 0;
            this.btnEditItemProperties.Click += new System.EventHandler(this.btnEditItemProperties_Click);
            //
            // lcEbayAccount
            //
            this.lcEbayAccount.Location = new System.Drawing.Point(116, 125);
            this.lcEbayAccount.MenuManager = this.ribbonControl1;
            this.lcEbayAccount.Name = "lcEbayAccount";
            this.lcEbayAccount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.lcEbayAccount.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.lcEbayAccount.Size = new System.Drawing.Size(339, 20);
            this.lcEbayAccount.StyleController = this.layoutControl1;
            this.lcEbayAccount.TabIndex = 1;
            //
            // lcItemID
            //
            this.lcItemID.AllowHtmlString = true;
            this.lcItemID.Location = new System.Drawing.Point(795, 12);
            this.lcItemID.Name = "lcItemID";
            this.lcItemID.Size = new System.Drawing.Size(207, 13);
            this.lcItemID.StyleController = this.layoutControl1;
            this.lcItemID.TabIndex = 1;
            this.lcItemID.Click += new System.EventHandler(this.lcItemID_Click);
            //
            // lcTerm
            //
            this.lcTerm.Location = new System.Drawing.Point(339, 132);
            this.lcTerm.Name = "lcTerm";
            this.lcTerm.Size = new System.Drawing.Size(0, 13);
            this.lcTerm.StyleController = this.layoutControl1;
            this.lcTerm.TabIndex = 1;
            //
            // lcTitle
            //
            this.lcTitle.Appearance.Font = new System.Drawing.Font("Tahoma", 10.25F);
            this.lcTitle.Appearance.Options.UseFont = true;
            this.lcTitle.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical;
            this.lcTitle.Location = new System.Drawing.Point(12, 12);
            this.lcTitle.Name = "lcTitle";
            this.lcTitle.Size = new System.Drawing.Size(779, 1);
            this.lcTitle.StyleController = this.layoutControl1;
            this.lcTitle.TabIndex = 1;
            //
            // lcCondition
            //
            this.lcCondition.Location = new System.Drawing.Point(74, 126);
            this.lcCondition.Name = "lcCondition";
            this.lcCondition.Size = new System.Drawing.Size(945, 20);
            this.lcCondition.StyleController = this.layoutControl1;
            this.lcCondition.TabIndex = 1;
            //
            // lcReturns
            //
            this.lcReturns.Location = new System.Drawing.Point(74, 78);
            this.lcReturns.Name = "lcReturns";
            this.lcReturns.Size = new System.Drawing.Size(945, 20);
            this.lcReturns.StyleController = this.layoutControl1;
            this.lcReturns.TabIndex = 1;
            //
            // lcBestOffer
            //
            this.lcBestOffer.Location = new System.Drawing.Point(116, 132);
            this.lcBestOffer.Name = "lcBestOffer";
            this.lcBestOffer.Size = new System.Drawing.Size(0, 13);
            this.lcBestOffer.StyleController = this.layoutControl1;
            this.lcBestOffer.TabIndex = 1;
            //
            // lcFoundTime
            //
            this.lcFoundTime.Location = new System.Drawing.Point(116, 132);
            this.lcFoundTime.Name = "lcFoundTime";
            this.lcFoundTime.Size = new System.Drawing.Size(0, 13);
            this.lcFoundTime.StyleController = this.layoutControl1;
            this.lcFoundTime.TabIndex = 1;
            //
            // lcLocation
            //
            this.lcLocation.Location = new System.Drawing.Point(74, 102);
            this.lcLocation.Name = "lcLocation";
            this.lcLocation.Size = new System.Drawing.Size(945, 20);
            this.lcLocation.StyleController = this.layoutControl1;
            this.lcLocation.TabIndex = 1;
            //
            // lcFromCountry
            //
            this.lcFromCountry.Location = new System.Drawing.Point(116, 98);
            this.lcFromCountry.Name = "lcFromCountry";
            this.lcFromCountry.Size = new System.Drawing.Size(0, 13);
            this.lcFromCountry.StyleController = this.layoutControl1;
            this.lcFromCountry.TabIndex = 1;
            //
            // lcToCountry
            //
            this.lcToCountry.Appearance.Options.UseTextOptions = true;
            this.lcToCountry.Appearance.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            this.lcToCountry.Location = new System.Drawing.Point(116, 115);
            this.lcToCountry.Name = "lcToCountry";
            this.lcToCountry.Size = new System.Drawing.Size(0, 13);
            this.lcToCountry.StyleController = this.layoutControl1;
            this.lcToCountry.TabIndex = 1;
            //
            // lcAutoPay
            //
            this.lcAutoPay.Location = new System.Drawing.Point(116, 132);
            this.lcAutoPay.Name = "lcAutoPay";
            this.lcAutoPay.Size = new System.Drawing.Size(0, 13);
            this.lcAutoPay.StyleController = this.layoutControl1;
            this.lcAutoPay.TabIndex = 1;
            //
            // lcCategoryID
            //
            this.lcCategoryID.Location = new System.Drawing.Point(116, 132);
            this.lcCategoryID.Name = "lcCategoryID";
            this.lcCategoryID.Size = new System.Drawing.Size(0, 13);
            this.lcCategoryID.StyleController = this.layoutControl1;
            this.lcCategoryID.TabIndex = 1;
            //
            // lcCategoryName
            //
            this.lcCategoryName.Location = new System.Drawing.Point(116, 132);
            this.lcCategoryName.Name = "lcCategoryName";
            this.lcCategoryName.Size = new System.Drawing.Size(0, 13);
            this.lcCategoryName.StyleController = this.layoutControl1;
            this.lcCategoryName.TabIndex = 1;
            //
            // lcConditionDescription
            //
            this.lcConditionDescription.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical;
            this.lcConditionDescription.Location = new System.Drawing.Point(74, 246);
            this.lcConditionDescription.Name = "lcConditionDescription";
            this.lcConditionDescription.Size = new System.Drawing.Size(945, 20);
            this.lcConditionDescription.StyleController = this.layoutControl1;
            this.lcConditionDescription.TabIndex = 1;
            //
            // lcFeedbackRating
            //
            this.lcFeedbackRating.AppearanceHovered.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.5F, ((System.Drawing.FontStyle)((System.Drawing.FontStyle.Bold | System.Drawing.FontStyle.Underline))));
            this.lcFeedbackRating.AppearanceHovered.ForeColor = System.Drawing.SystemColors.MenuHighlight;
            this.lcFeedbackRating.AppearanceHovered.Options.UseFont = true;
            this.lcFeedbackRating.AppearanceHovered.Options.UseForeColor = true;
            this.lcFeedbackRating.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lcFeedbackRating.Location = new System.Drawing.Point(402, 54);
            this.lcFeedbackRating.Name = "lcFeedbackRating";
            this.lcFeedbackRating.Size = new System.Drawing.Size(393, 20);
            this.lcFeedbackRating.StyleController = this.layoutControl1;
            this.lcFeedbackRating.TabIndex = 1;
            this.lcFeedbackRating.Click += new System.EventHandler(this.OpenTagLink);
            //
            // lcFeedbackScore
            //
            this.lcFeedbackScore.AppearanceHovered.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.5F, ((System.Drawing.FontStyle)((System.Drawing.FontStyle.Bold | System.Drawing.FontStyle.Underline))));
            this.lcFeedbackScore.AppearanceHovered.ForeColor = System.Drawing.SystemColors.MenuHighlight;
            this.lcFeedbackScore.AppearanceHovered.Options.UseFont = true;
            this.lcFeedbackScore.AppearanceHovered.Options.UseForeColor = true;
            this.lcFeedbackScore.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lcFeedbackScore.Location = new System.Drawing.Point(258, 54);
            this.lcFeedbackScore.Name = "lcFeedbackScore";
            this.lcFeedbackScore.Size = new System.Drawing.Size(139, 13);
            this.lcFeedbackScore.StyleController = this.layoutControl1;
            this.lcFeedbackScore.TabIndex = 1;
            this.lcFeedbackScore.Click += new System.EventHandler(this.OpenTagLink);
            //
            // lcItemPrice
            //
            this.lcItemPrice.Location = new System.Drawing.Point(116, 47);
            this.lcItemPrice.Name = "lcItemPrice";
            this.lcItemPrice.Size = new System.Drawing.Size(0, 13);
            this.lcItemPrice.StyleController = this.layoutControl1;
            this.lcItemPrice.TabIndex = 1;
            //
            // lcPostedTime
            //
            this.lcPostedTime.Location = new System.Drawing.Point(116, 132);
            this.lcPostedTime.Name = "lcPostedTime";
            this.lcPostedTime.Size = new System.Drawing.Size(0, 13);
            this.lcPostedTime.StyleController = this.layoutControl1;
            this.lcPostedTime.TabIndex = 1;
            //
            // lcQuantity
            //
            this.lcQuantity.Location = new System.Drawing.Point(74, 222);
            this.lcQuantity.Name = "lcQuantity";
            this.lcQuantity.Size = new System.Drawing.Size(945, 20);
            this.lcQuantity.StyleController = this.layoutControl1;
            this.lcQuantity.TabIndex = 1;
            //
            // lcSellerName
            //
            this.lcSellerName.AppearanceHovered.Font = new System.Drawing.Font("Tahoma", 8.5F, ((System.Drawing.FontStyle)((System.Drawing.FontStyle.Bold | System.Drawing.FontStyle.Underline))), System.Drawing.GraphicsUnit.Point, ((byte)(204)));
            this.lcSellerName.AppearanceHovered.ForeColor = System.Drawing.SystemColors.MenuHighlight;
            this.lcSellerName.AppearanceHovered.Options.UseFont = true;
            this.lcSellerName.AppearanceHovered.Options.UseForeColor = true;
            this.lcSellerName.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lcSellerName.Location = new System.Drawing.Point(74, 54);
            this.lcSellerName.Name = "lcSellerName";
            this.lcSellerName.Size = new System.Drawing.Size(141, 20);
            this.lcSellerName.StyleController = this.layoutControl1;
            this.lcSellerName.TabIndex = 1;
            this.lcSellerName.Click += new System.EventHandler(this.OpenTagLink);
            //
            // lcShipping
            //
            this.lcShipping.Location = new System.Drawing.Point(116, 47);
            this.lcShipping.Name = "lcShipping";
            this.lcShipping.Size = new System.Drawing.Size(0, 13);
            this.lcShipping.StyleController = this.layoutControl1;
            this.lcShipping.TabIndex = 1;
            //
            // lcShippingType
            //
            this.lcShippingType.Location = new System.Drawing.Point(116, 132);
            this.lcShippingType.Name = "lcShippingType";
            this.lcShippingType.Size = new System.Drawing.Size(0, 13);
            this.lcShippingType.StyleController = this.layoutControl1;
            this.lcShippingType.TabIndex = 1;
            //
            // lcShipAdditionalItem
            //
            this.lcShipAdditionalItem.Location = new System.Drawing.Point(339, 132);
            this.lcShipAdditionalItem.Name = "lcShipAdditionalItem";
            this.lcShipAdditionalItem.Size = new System.Drawing.Size(0, 13);
            this.lcShipAdditionalItem.StyleController = this.layoutControl1;
            this.lcShipAdditionalItem.TabIndex = 1;
            //
            // lcSoldTime
            //
            this.lcSoldTime.Location = new System.Drawing.Point(339, 132);
            this.lcSoldTime.Name = "lcSoldTime";
            this.lcSoldTime.Size = new System.Drawing.Size(0, 13);
            this.lcSoldTime.StyleController = this.layoutControl1;
            this.lcSoldTime.TabIndex = 1;
            //
            // lcEbayWebsite
            //
            this.lcEbayWebsite.Location = new System.Drawing.Point(116, 132);
            this.lcEbayWebsite.Name = "lcEbayWebsite";
            this.lcEbayWebsite.Size = new System.Drawing.Size(0, 13);
            this.lcEbayWebsite.StyleController = this.layoutControl1;
            this.lcEbayWebsite.TabIndex = 1;
            //
            // lcPageViews
            //
            this.lcPageViews.Location = new System.Drawing.Point(116, 115);
            this.lcPageViews.Name = "lcPageViews";
            this.lcPageViews.Size = new System.Drawing.Size(0, 13);
            this.lcPageViews.StyleController = this.layoutControl1;
            this.lcPageViews.TabIndex = 1;
            //
            // lcUPC
            //
            this.lcUPC.Location = new System.Drawing.Point(339, 144);
            this.lcUPC.Name = "lcUPC";
            this.lcUPC.Size = new System.Drawing.Size(0, 13);
            this.lcUPC.StyleController = this.layoutControl1;
            this.lcUPC.TabIndex = 1;
            //
            // lcVariation
            //
            this.lcVariation.Location = new System.Drawing.Point(339, 144);
            this.lcVariation.Name = "lcVariation";
            this.lcVariation.Size = new System.Drawing.Size(0, 13);
            this.lcVariation.StyleController = this.layoutControl1;
            this.lcVariation.TabIndex = 1;
            //
            // lcTotalPrice
            //
            this.lcTotalPrice.Location = new System.Drawing.Point(74, 30);
            this.lcTotalPrice.Name = "lcTotalPrice";
            this.lcTotalPrice.Size = new System.Drawing.Size(945, 20);
            this.lcTotalPrice.StyleController = this.layoutControl1;
            this.lcTotalPrice.TabIndex = 1;
            //
            // lciVariation
            //
            this.lciVariation.Control = this.lcVariation;
            this.lciVariation.CustomizationFormText = "Variation";
            this.lciVariation.Location = new System.Drawing.Point(223, 398);
            this.lciVariation.Name = "lciVariation";
            this.lciVariation.Size = new System.Drawing.Size(224, 17);
            this.lciVariation.Text = "Variation";
            this.lciVariation.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciUPC
            //
            this.lciUPC.Control = this.lcUPC;
            this.lciUPC.CustomizationFormText = "UPC";
            this.lciUPC.Location = new System.Drawing.Point(223, 381);
            this.lciUPC.Name = "lciUPC";
            this.lciUPC.Size = new System.Drawing.Size(224, 17);
            this.lciUPC.Text = "UPC";
            this.lciUPC.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciTerm
            //
            this.lciTerm.Control = this.lcTerm;
            this.lciTerm.CustomizationFormText = "Term";
            this.lciTerm.Location = new System.Drawing.Point(223, 364);
            this.lciTerm.Name = "lciTerm";
            this.lciTerm.Size = new System.Drawing.Size(224, 17);
            this.lciTerm.Text = "Term";
            this.lciTerm.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciSoldTime
            //
            this.lciSoldTime.Control = this.lcSoldTime;
            this.lciSoldTime.CustomizationFormText = "Sold Time";
            this.lciSoldTime.Location = new System.Drawing.Point(223, 347);
            this.lciSoldTime.Name = "lciSoldTime";
            this.lciSoldTime.Size = new System.Drawing.Size(224, 17);
            this.lciSoldTime.Text = "Sold Time";
            this.lciSoldTime.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciShipAdditionalItem
            //
            this.lciShipAdditionalItem.Control = this.lcShipAdditionalItem;
            this.lciShipAdditionalItem.CustomizationFormText = "Ship Additional Item";
            this.lciShipAdditionalItem.Location = new System.Drawing.Point(223, 330);
            this.lciShipAdditionalItem.Name = "lciShipAdditionalItem";
            this.lciShipAdditionalItem.Size = new System.Drawing.Size(224, 17);
            this.lciShipAdditionalItem.Text = "Ship Additional Item";
            this.lciShipAdditionalItem.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciShippingType
            //
            this.lciShippingType.Control = this.lcShippingType;
            this.lciShippingType.CustomizationFormText = "Shipping Type";
            this.lciShippingType.Location = new System.Drawing.Point(0, 330);
            this.lciShippingType.Name = "lciShippingType";
            this.lciShippingType.Size = new System.Drawing.Size(447, 17);
            this.lciShippingType.Text = "Shipping Type";
            this.lciShippingType.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciPageViews
            //
            this.lciPageViews.Control = this.lcPageViews;
            this.lciPageViews.CustomizationFormText = "Page Views";
            this.lciPageViews.Location = new System.Drawing.Point(0, 279);
            this.lciPageViews.Name = "lciPageViews";
            this.lciPageViews.Size = new System.Drawing.Size(447, 17);
            this.lciPageViews.Text = "Page Views";
            this.lciPageViews.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciPostedTime
            //
            this.lciPostedTime.Control = this.lcPostedTime;
            this.lciPostedTime.CustomizationFormText = "Posted Time";
            this.lciPostedTime.Location = new System.Drawing.Point(0, 279);
            this.lciPostedTime.Name = "lciPostedTime";
            this.lciPostedTime.Size = new System.Drawing.Size(447, 17);
            this.lciPostedTime.Text = "Posted Time";
            this.lciPostedTime.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciFoundTime
            //
            this.lciFoundTime.Control = this.lcFoundTime;
            this.lciFoundTime.CustomizationFormText = "Found Time";
            this.lciFoundTime.Location = new System.Drawing.Point(0, 245);
            this.lciFoundTime.Name = "lciFoundTime";
            this.lciFoundTime.Size = new System.Drawing.Size(447, 17);
            this.lciFoundTime.Text = "Found Time";
            this.lciFoundTime.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciEbayWebsite
            //
            this.lciEbayWebsite.Control = this.lcEbayWebsite;
            this.lciEbayWebsite.CustomizationFormText = "Ebay Website";
            this.lciEbayWebsite.Location = new System.Drawing.Point(0, 228);
            this.lciEbayWebsite.Name = "lciEbayWebsite";
            this.lciEbayWebsite.Size = new System.Drawing.Size(447, 17);
            this.lciEbayWebsite.Text = "Ebay Website";
            this.lciEbayWebsite.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciEbayAccount
            //
            this.lciEbayAccount.Control = this.lcEbayAccount;
            this.lciEbayAccount.Location = new System.Drawing.Point(0, 204);
            this.lciEbayAccount.Name = "lciEbayAccount";
            this.lciEbayAccount.Size = new System.Drawing.Size(447, 24);
            this.lciEbayAccount.Text = "eBay Account";
            this.lciEbayAccount.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciCategoryName
            //
            this.lciCategoryName.Control = this.lcCategoryName;
            this.lciCategoryName.CustomizationFormText = "Category Name";
            this.lciCategoryName.Location = new System.Drawing.Point(0, 187);
            this.lciCategoryName.Name = "lciCategoryName";
            this.lciCategoryName.Size = new System.Drawing.Size(447, 17);
            this.lciCategoryName.Text = "Category Name";
            this.lciCategoryName.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciCategoryID
            //
            this.lciCategoryID.Control = this.lcCategoryID;
            this.lciCategoryID.CustomizationFormText = "Category ID";
            this.lciCategoryID.Location = new System.Drawing.Point(0, 170);
            this.lciCategoryID.Name = "lciCategoryID";
            this.lciCategoryID.Size = new System.Drawing.Size(447, 17);
            this.lciCategoryID.Text = "Category ID";
            this.lciCategoryID.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciBestOffer
            //
            this.lciBestOffer.Control = this.lcBestOffer;
            this.lciBestOffer.CustomizationFormText = "Best Offer";
            this.lciBestOffer.Location = new System.Drawing.Point(0, 153);
            this.lciBestOffer.Name = "lciBestOffer";
            this.lciBestOffer.Size = new System.Drawing.Size(447, 17);
            this.lciBestOffer.Text = "Best Offer";
            this.lciBestOffer.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciAutoPay
            //
            this.lciAutoPay.Control = this.lcAutoPay;
            this.lciAutoPay.CustomizationFormText = "Commit To Buy";
            this.lciAutoPay.Location = new System.Drawing.Point(0, 136);
            this.lciAutoPay.Name = "lciAutoPay";
            this.lciAutoPay.Size = new System.Drawing.Size(447, 17);
            this.lciAutoPay.Text = "Commit To Buy";
            this.lciAutoPay.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciItemPrice
            //
            this.lciItemPrice.Control = this.lcItemPrice;
            this.lciItemPrice.CustomizationFormText = "Item Price";
            this.lciItemPrice.Location = new System.Drawing.Point(0, 35);
            this.lciItemPrice.Name = "lciItemPrice";
            this.lciItemPrice.Size = new System.Drawing.Size(475, 17);
            this.lciItemPrice.Text = "Item Price";
            this.lciItemPrice.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciShipping
            //
            this.lciShipping.Control = this.lcShipping;
            this.lciShipping.CustomizationFormText = "Shipping";
            this.lciShipping.FillControlToClientArea = false;
            this.lciShipping.Location = new System.Drawing.Point(0, 35);
            this.lciShipping.Name = "lciShipping";
            this.lciShipping.Size = new System.Drawing.Size(475, 17);
            this.lciShipping.Text = "Shipping";
            this.lciShipping.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciToCountry
            //
            this.lciToCountry.Control = this.lcToCountry;
            this.lciToCountry.CustomizationFormText = "To Country";
            this.lciToCountry.Location = new System.Drawing.Point(0, 103);
            this.lciToCountry.Name = "lciToCountry";
            this.lciToCountry.Size = new System.Drawing.Size(475, 17);
            this.lciToCountry.Text = "To Country";
            this.lciToCountry.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciFromCountry
            //
            this.lciFromCountry.Control = this.lcFromCountry;
            this.lciFromCountry.CustomizationFormText = "From Country";
            this.lciFromCountry.Location = new System.Drawing.Point(0, 86);
            this.lciFromCountry.Name = "lciFromCountry";
            this.lciFromCountry.Size = new System.Drawing.Size(475, 17);
            this.lciFromCountry.Text = "From Country";
            this.lciFromCountry.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciPayment
            //
            this.lciPayment.Control = this.lcPayment;
            this.lciPayment.CustomizationFormText = "Payment";
            this.lciPayment.Location = new System.Drawing.Point(0, 120);
            this.lciPayment.Name = "lciPayment";
            this.lciPayment.Size = new System.Drawing.Size(475, 17);
            this.lciPayment.Text = "Payment";
            this.lciPayment.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciShippingDays
            //
            this.lciShippingDays.Control = this.lcShippingDays;
            this.lciShippingDays.CustomizationFormText = "Shipping Days";
            this.lciShippingDays.Location = new System.Drawing.Point(0, 171);
            this.lciShippingDays.Name = "lciShippingDays";
            this.lciShippingDays.Size = new System.Drawing.Size(1028, 17);
            this.lciShippingDays.Text = "Shipping Days";
            this.lciShippingDays.TextSize = new System.Drawing.Size(50, 20);
            //
            // layoutControlGroup1
            //
            this.layoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.lciTotalPrice,
            this.lciConditionDescription,
            this.lciCondition,
            this.lciReturns,
            this.lciTitle,
            this.simpleSeparator1,
            this.lciSellerName,
            this.lciLocation,
            this.layoutControlItem1,
            this.lciItemID,
            this.lciFeedbackScore,
            this.lciFeedbackRating,
            this.lciQuantity,
            this.simpleSeparator2,
            this.simpleSeparator3,
            this.emptySpaceItem1,
            this.emptySpaceItem2,
            this.lciAuctionPrice,
            this.lciListingType,
            this.lciBids});
            this.layoutControlGroup1.Name = "Root";
            this.layoutControlGroup1.Size = new System.Drawing.Size(1031, 278);
            //
            // lciTotalPrice
            //
            this.lciTotalPrice.Control = this.lcTotalPrice;
            this.lciTotalPrice.CustomizationFormText = "Total Price";
            this.lciTotalPrice.Location = new System.Drawing.Point(0, 18);
            this.lciTotalPrice.Name = "lciTotalPrice";
            this.lciTotalPrice.Size = new System.Drawing.Size(1011, 24);
            this.lciTotalPrice.Text = "Total Price";
            this.lciTotalPrice.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciConditionDescription
            //
            this.lciConditionDescription.Control = this.lcConditionDescription;
            this.lciConditionDescription.CustomizationFormText = "Condition Description";
            this.lciConditionDescription.Location = new System.Drawing.Point(0, 234);
            this.lciConditionDescription.Name = "lciConditionDescription";
            this.lciConditionDescription.Size = new System.Drawing.Size(1011, 24);
            this.lciConditionDescription.StartNewLine = true;
            this.lciConditionDescription.Text = "Condition Description";
            this.lciConditionDescription.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciCondition
            //
            this.lciCondition.Control = this.lcCondition;
            this.lciCondition.CustomizationFormText = "Condition";
            this.lciCondition.Location = new System.Drawing.Point(0, 114);
            this.lciCondition.Name = "lciCondition";
            this.lciCondition.Size = new System.Drawing.Size(1011, 24);
            this.lciCondition.Text = "Condition";
            this.lciCondition.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciReturns
            //
            this.lciReturns.Control = this.lcReturns;
            this.lciReturns.CustomizationFormText = "Returns";
            this.lciReturns.Location = new System.Drawing.Point(0, 66);
            this.lciReturns.Name = "lciReturns";
            this.lciReturns.Size = new System.Drawing.Size(1011, 24);
            this.lciReturns.Text = "Returns";
            this.lciReturns.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciTitle
            //
            this.lciTitle.Control = this.lcTitle;
            this.lciTitle.CustomizationFormText = "Title";
            this.lciTitle.Location = new System.Drawing.Point(0, 0);
            this.lciTitle.Name = "lciTitle";
            this.lciTitle.Size = new System.Drawing.Size(783, 17);
            this.lciTitle.StartNewLine = true;
            this.lciTitle.Text = "Title";
            this.lciTitle.TextVisible = false;
            //
            // simpleSeparator1
            //
            this.simpleSeparator1.Location = new System.Drawing.Point(0, 17);
            this.simpleSeparator1.Name = "simpleSeparator1";
            this.simpleSeparator1.Size = new System.Drawing.Size(1011, 1);
            //
            // lciSellerName
            //
            this.lciSellerName.Control = this.lcSellerName;
            this.lciSellerName.CustomizationFormText = "Seller Name";
            this.lciSellerName.Location = new System.Drawing.Point(0, 42);
            this.lciSellerName.Name = "lciSellerName";
            this.lciSellerName.Size = new System.Drawing.Size(207, 24);
            this.lciSellerName.Text = "Seller";
            this.lciSellerName.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciLocation
            //
            this.lciLocation.Control = this.lcLocation;
            this.lciLocation.CustomizationFormText = "Location";
            this.lciLocation.Location = new System.Drawing.Point(0, 90);
            this.lciLocation.Name = "lciLocation";
            this.lciLocation.Size = new System.Drawing.Size(1011, 24);
            this.lciLocation.Text = "Location";
            this.lciLocation.TextSize = new System.Drawing.Size(50, 20);
            //
            // layoutControlItem1
            //
            this.layoutControlItem1.Control = this.btnEditItemProperties;
            this.layoutControlItem1.ImageOptions.Alignment = System.Drawing.ContentAlignment.MiddleCenter;
            this.layoutControlItem1.Location = new System.Drawing.Point(994, 0);
            this.layoutControlItem1.MaxSize = new System.Drawing.Size(18, 18);
            this.layoutControlItem1.MinSize = new System.Drawing.Size(16, 16);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.ShowInCustomizationForm = false;
            this.layoutControlItem1.Size = new System.Drawing.Size(17, 17);
            this.layoutControlItem1.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem1.TextVisible = false;
            //
            // lciItemID
            //
            this.lciItemID.Control = this.lcItemID;
            this.lciItemID.Location = new System.Drawing.Point(783, 0);
            this.lciItemID.Name = "lciItemID";
            this.lciItemID.Size = new System.Drawing.Size(211, 17);
            this.lciItemID.Text = "ItemID";
            this.lciItemID.TextVisible = false;
            //
            // lciFeedbackScore
            //
            this.lciFeedbackScore.Control = this.lcFeedbackScore;
            this.lciFeedbackScore.CustomizationFormText = "Feedback Score";
            this.lciFeedbackScore.Location = new System.Drawing.Point(246, 42);
            this.lciFeedbackScore.Name = "lciFeedbackScore";
            this.lciFeedbackScore.Size = new System.Drawing.Size(143, 24);
            this.lciFeedbackScore.Text = "Feedback Count";
            this.lciFeedbackScore.TextVisible = false;
            //
            // lciFeedbackRating
            //
            this.lciFeedbackRating.Control = this.lcFeedbackRating;
            this.lciFeedbackRating.CustomizationFormText = "Feedback Rating";
            this.lciFeedbackRating.Location = new System.Drawing.Point(390, 42);
            this.lciFeedbackRating.Name = "lciFeedbackRating";
            this.lciFeedbackRating.Size = new System.Drawing.Size(459, 24);
            this.lciFeedbackRating.Text = "%";
            this.lciFeedbackRating.TextLocation = DevExpress.Utils.Locations.Right;
            this.lciFeedbackRating.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciQuantity
            //
            this.lciQuantity.Control = this.lcQuantity;
            this.lciQuantity.CustomizationFormText = "Quantity";
            this.lciQuantity.Location = new System.Drawing.Point(0, 210);
            this.lciQuantity.Name = "lciQuantity";
            this.lciQuantity.Size = new System.Drawing.Size(1011, 24);
            this.lciQuantity.Text = "Quantity";
            this.lciQuantity.TextSize = new System.Drawing.Size(50, 20);
            //
            // simpleSeparator2
            //
            this.simpleSeparator2.Location = new System.Drawing.Point(245, 42);
            this.simpleSeparator2.Name = "simpleSeparator2";
            this.simpleSeparator2.Size = new System.Drawing.Size(1, 24);
            //
            // simpleSeparator3
            //
            this.simpleSeparator3.Location = new System.Drawing.Point(389, 42);
            this.simpleSeparator3.Name = "simpleSeparator3";
            this.simpleSeparator3.Size = new System.Drawing.Size(1, 24);
            //
            // emptySpaceItem1
            //
            this.emptySpaceItem1.Location = new System.Drawing.Point(849, 42);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(162, 24);
            //
            // emptySpaceItem2
            //
            this.emptySpaceItem2.Location = new System.Drawing.Point(207, 42);
            this.emptySpaceItem2.Name = "emptySpaceItem2";
            this.emptySpaceItem2.Size = new System.Drawing.Size(38, 24);
            //
            // lciAuctionPrice
            //
            this.lciAuctionPrice.Control = this.lcAuctionPrice;
            this.lciAuctionPrice.Location = new System.Drawing.Point(0, 138);
            this.lciAuctionPrice.Name = "lciAuctionPrice";
            this.lciAuctionPrice.Size = new System.Drawing.Size(1011, 24);
            this.lciAuctionPrice.Text = "Auction Price";
            this.lciAuctionPrice.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciListingType
            //
            this.lciListingType.Control = this.lcListingType;
            this.lciListingType.Location = new System.Drawing.Point(0, 162);
            this.lciListingType.Name = "lciListingType";
            this.lciListingType.Size = new System.Drawing.Size(1011, 24);
            this.lciListingType.Text = "Listing Type";
            this.lciListingType.TextSize = new System.Drawing.Size(50, 20);
            //
            // lciBids
            //
            this.lciBids.Control = this.lcBids;
            this.lciBids.Location = new System.Drawing.Point(0, 186);
            this.lciBids.Name = "lciBids";
            this.lciBids.Size = new System.Drawing.Size(1011, 24);
            this.lciBids.Text = "Bids";
            this.lciBids.TextSize = new System.Drawing.Size(50, 20);
            //
            // lcipanelPicturesControl
            //
            this.lcipanelPicturesControl.Control = this.panelPicturesControl;
            this.lcipanelPicturesControl.Location = new System.Drawing.Point(0, 0);
            this.lcipanelPicturesControl.Name = "lcipanelPicturesControl";
            this.lcipanelPicturesControl.Size = new System.Drawing.Size(935, 171);
            this.lcipanelPicturesControl.TextLocation = DevExpress.Utils.Locations.Left;
            this.lcipanelPicturesControl.TextVisible = false;
            //
            // panelPicturesControl
            //
            this.panelPicturesControl.Controls.Add(this.galleryControl1);
            this.panelPicturesControl.Location = new System.Drawing.Point(2, 2);
            this.panelPicturesControl.Name = "panelPicturesControl";
            this.panelPicturesControl.Size = new System.Drawing.Size(931, 167);
            this.panelPicturesControl.TabIndex = 0;
            //
            // layoutControlForPicture
            //
            this.layoutControlForPicture.Controls.Add(this.panelPicturesControl);
            this.layoutControlForPicture.Controls.Add(this.panelPicturesSettingControl);
            this.layoutControlForPicture.Controls.Add(this.btnEditPictureProperties);
            this.layoutControlForPicture.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutControlForPicture.LayoutVersion = "2";
            this.layoutControlForPicture.Location = new System.Drawing.Point(0, 0);
            this.layoutControlForPicture.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.layoutControlForPicture.Name = "layoutControlForPicture";
            this.layoutControlForPicture.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = new System.Drawing.Rectangle(1188, 273, 1158, 968);
            this.layoutControlForPicture.OptionsCustomizationForm.ShowPropertyGrid = true;
            this.layoutControlForPicture.OptionsItemText.TextAlignMode = DevExpress.XtraLayout.TextAlignMode.CustomSize;
            this.layoutControlForPicture.OptionsSerialization.RestoreAppearanceItemCaption = true;
            this.layoutControlForPicture.OptionsSerialization.RestoreAppearanceTabPage = true;
            this.layoutControlForPicture.OptionsSerialization.RestoreGroupPadding = true;
            this.layoutControlForPicture.OptionsSerialization.RestoreGroupSpacing = true;
            this.layoutControlForPicture.OptionsSerialization.RestoreLayoutGroupAppearanceGroup = true;
            this.layoutControlForPicture.OptionsSerialization.RestoreLayoutItemPadding = true;
            this.layoutControlForPicture.OptionsSerialization.RestoreLayoutItemSpacing = true;
            this.layoutControlForPicture.OptionsSerialization.RestoreLayoutItemText = false;
            this.layoutControlForPicture.OptionsSerialization.RestoreRootGroupPadding = true;
            this.layoutControlForPicture.OptionsSerialization.RestoreRootGroupSpacing = true;
            this.layoutControlForPicture.OptionsSerialization.RestoreTabbedGroupPadding = true;
            this.layoutControlForPicture.OptionsSerialization.RestoreTabbedGroupSpacing = true;
            this.layoutControlForPicture.OptionsSerialization.RestoreTextToControlDistance = true;
            this.layoutControlForPicture.Root = this.layoutControlGroupPictures;
            this.layoutControlForPicture.Size = new System.Drawing.Size(1048, 171);
            this.layoutControlForPicture.TabIndex = 110;
            this.layoutControlForPicture.Text = "layoutControlForPicture";
            //
            // panelPicturesSettingControl
            //
            this.panelPicturesSettingControl.Controls.Add(this.zoomTrackBarExpanded);
            this.panelPicturesSettingControl.Controls.Add(this.zoomTrackBarPictures);
            this.panelPicturesSettingControl.Controls.Add(this.svgLargeImageBox);
            this.panelPicturesSettingControl.Controls.Add(this.svgSmallImageBox);
            this.panelPicturesSettingControl.Location = new System.Drawing.Point(937, 2);
            this.panelPicturesSettingControl.MaximumSize = new System.Drawing.Size(85, 147);
            this.panelPicturesSettingControl.MinimumSize = new System.Drawing.Size(75, 140);
            this.panelPicturesSettingControl.Name = "panelPicturesSettingControl";
            this.panelPicturesSettingControl.Size = new System.Drawing.Size(85, 147);
            this.panelPicturesSettingControl.TabIndex = 2;
            this.panelPicturesSettingControl.Visible = false;
            //
            // zoomTrackBarExpanded
            //
            this.zoomTrackBarExpanded.EditValue = 20;
            this.zoomTrackBarExpanded.Location = new System.Drawing.Point(43, 38);
            this.zoomTrackBarExpanded.Margin = new System.Windows.Forms.Padding(4);
            this.zoomTrackBarExpanded.Name = "zoomTrackBarExpanded";
            this.zoomTrackBarExpanded.Properties.AllowFocused = false;
            this.zoomTrackBarExpanded.Properties.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.zoomTrackBarExpanded.Properties.Appearance.Options.UseBackColor = true;
            this.zoomTrackBarExpanded.Properties.AutoSize = false;
            this.zoomTrackBarExpanded.Properties.LargeChange = 1;
            this.zoomTrackBarExpanded.Properties.Maximum = 100;
            this.zoomTrackBarExpanded.Properties.Middle = 55;
            this.zoomTrackBarExpanded.Properties.Minimum = 10;
            this.zoomTrackBarExpanded.Properties.Orientation = System.Windows.Forms.Orientation.Vertical;
            this.zoomTrackBarExpanded.Size = new System.Drawing.Size(35, 100);
            this.zoomTrackBarExpanded.StyleController = this.layoutControlForPicture;
            this.zoomTrackBarExpanded.TabIndex = 21;
            this.zoomTrackBarExpanded.Value = 20;
            //
            // svgLargeImageBox
            //
            this.svgLargeImageBox.Location = new System.Drawing.Point(43, 4);
            this.svgLargeImageBox.Name = "svgLargeImageBox";
            this.svgLargeImageBox.Size = new System.Drawing.Size(33, 33);
            this.svgLargeImageBox.SizeMode = DevExpress.XtraEditors.SvgImageSizeMode.Squeeze;
            this.svgLargeImageBox.SvgImage = global::uBuyFirst.Properties.Resources.Expand;
            this.svgLargeImageBox.TabIndex = 112;
            this.svgLargeImageBox.Text = "svgImageBox2";
            //
            // svgSmallImageBox
            //
            this.svgSmallImageBox.Location = new System.Drawing.Point(3, 4);
            this.svgSmallImageBox.Name = "svgSmallImageBox";
            this.svgSmallImageBox.Size = new System.Drawing.Size(33, 33);
            this.svgSmallImageBox.SizeMode = DevExpress.XtraEditors.SvgImageSizeMode.Squeeze;
            this.svgSmallImageBox.SvgImage = global::uBuyFirst.Properties.Resources.NormalSize;
            this.svgSmallImageBox.TabIndex = 1;
            this.svgSmallImageBox.Text = "svgImageBox1";
            //
            // btnEditPictureProperties
            //
            this.btnEditPictureProperties.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnEditPictureProperties.ImageOptions.Image")));
            this.btnEditPictureProperties.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnEditPictureProperties.Location = new System.Drawing.Point(1026, 2);
            this.btnEditPictureProperties.MaximumSize = new System.Drawing.Size(20, 20);
            this.btnEditPictureProperties.MinimumSize = new System.Drawing.Size(12, 12);
            this.btnEditPictureProperties.Name = "btnEditPictureProperties";
            this.btnEditPictureProperties.Size = new System.Drawing.Size(20, 20);
            this.btnEditPictureProperties.StyleController = this.layoutControlForPicture;
            this.btnEditPictureProperties.TabIndex = 3;
            this.btnEditPictureProperties.Click += new System.EventHandler(this.btnEditPictureProperties_Click);
            //
            // layoutControlGroupPictures
            //
            this.layoutControlGroupPictures.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.layoutControlGroupPictures.GroupBordersVisible = false;
            this.layoutControlGroupPictures.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.lcipanelPicturesControl,
            this.lcipanelPicturesOpenSettingsControl,
            this.lcipanelPicturesSettingControl});
            this.layoutControlGroupPictures.Name = "layoutControlGroupPictures";
            this.layoutControlGroupPictures.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.layoutControlGroupPictures.Size = new System.Drawing.Size(1048, 171);
            this.layoutControlGroupPictures.TextVisible = false;
            //
            // lcipanelPicturesOpenSettingsControl
            //
            this.lcipanelPicturesOpenSettingsControl.Control = this.btnEditPictureProperties;
            this.lcipanelPicturesOpenSettingsControl.Location = new System.Drawing.Point(1024, 0);
            this.lcipanelPicturesOpenSettingsControl.Name = "lcipanelPicturesOpenSettingsControl";
            this.lcipanelPicturesOpenSettingsControl.Size = new System.Drawing.Size(24, 171);
            this.lcipanelPicturesOpenSettingsControl.TextVisible = false;
            //
            // lcipanelPicturesSettingControl
            //
            this.lcipanelPicturesSettingControl.Control = this.panelPicturesSettingControl;
            this.lcipanelPicturesSettingControl.Location = new System.Drawing.Point(935, 0);
            this.lcipanelPicturesSettingControl.Name = "panelPicturesSettingControl";
            this.lcipanelPicturesSettingControl.Size = new System.Drawing.Size(89, 171);
            this.lcipanelPicturesSettingControl.TextVisible = false;
            this.lcipanelPicturesSettingControl.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never;
            //
            // pictureSettingsButton2
            //
            this.pictureSettingsButton2.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("pictureSettingsButton2.ImageOptions.Image")));
            this.pictureSettingsButton2.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.pictureSettingsButton2.Location = new System.Drawing.Point(74, 12);
            this.pictureSettingsButton2.Margin = new System.Windows.Forms.Padding(4);
            this.pictureSettingsButton2.MaximumSize = new System.Drawing.Size(50, 50);
            this.pictureSettingsButton2.Name = "pictureSettingsButton2";
            this.pictureSettingsButton2.Size = new System.Drawing.Size(50, 50);
            this.pictureSettingsButton2.StyleController = this.layoutControlForPicture;
            this.pictureSettingsButton2.TabIndex = 0;
            this.pictureSettingsButton2.Click += new System.EventHandler(this.pictureSettingsButton_Click);
            //
            // dockManager1
            //
            this.dockManager1.AllowGlyphSkinning = true;
            this.dockManager1.Controller = this.barAndDockingController1;
            this.dockManager1.DockingOptions.ShowMaximizeButton = false;
            this.dockManager1.Form = this;
            this.dockManager1.RootPanels.AddRange(new DevExpress.XtraBars.Docking.DockPanel[] {
            this.dockFilters,
            this.dockSearchQueries,
            this.dockDescription,
            this.dockItemProperties,
            this.dockLog,
            this.dockPictures,
            this.dockPanelBuy,
            this.dockPanelExternalData,
            this.dockPanelWatchlist});
            this.dockManager1.TopZIndexControls.AddRange(new string[] {
            "DevExpress.XtraBars.BarDockControl",
            "DevExpress.XtraBars.StandaloneBarDockControl",
            "System.Windows.Forms.StatusBar",
            "System.Windows.Forms.MenuStrip",
            "System.Windows.Forms.StatusStrip",
            "DevExpress.XtraBars.Ribbon.RibbonStatusBar",
            "DevExpress.XtraBars.Ribbon.RibbonControl",
            "DevExpress.XtraBars.Navigation.OfficeNavigationBar",
            "DevExpress.XtraBars.Navigation.TileNavPane"});
            //
            // dockDescription
            //
            this.dockDescription.Controls.Add(this.dockPanel5_Container);
            this.dockDescription.DockedAsTabbedDocument = true;
            this.dockDescription.FloatLocation = new System.Drawing.Point(1190, 363);
            this.dockDescription.FloatSize = new System.Drawing.Size(758, 216);
            this.dockDescription.FloatVertical = true;
            this.dockDescription.Header = "Description";
            this.dockDescription.ID = new System.Guid("a573c809-9fb9-446b-acf0-cbd7aad812ce");
            this.dockDescription.Name = "dockDescription";
            this.dockDescription.OriginalSize = new System.Drawing.Size(504, 332);
            this.dockDescription.SavedIndex = 2;
            this.dockDescription.SavedMdiDocument = true;
            this.dockDescription.Text = "Description";
            this.dockDescription.CustomButtonClick += new DevExpress.XtraBars.Docking2010.ButtonEventHandler(this.dockDescription_CustomButtonClick);
            //
            // dockPanel5_Container
            //
            this.dockPanel5_Container.BackColor = System.Drawing.Color.Transparent;
            this.dockPanel5_Container.Controls.Add(this.splitContainerControlBrowser);
            this.dockPanel5_Container.Controls.Add(this.flyoutPanelBrowser);
            this.dockPanel5_Container.Controls.Add(this.btnBrowserSettings);
            this.dockPanel5_Container.Location = new System.Drawing.Point(0, 0);
            this.dockPanel5_Container.Name = "dockPanel5_Container";
            this.dockPanel5_Container.Size = new System.Drawing.Size(1048, 211);
            this.dockPanel5_Container.TabIndex = 0;
            //
            // splitContainerControlBrowser
            //
            this.splitContainerControlBrowser.Collapsed = true;
            this.splitContainerControlBrowser.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel2;
            this.splitContainerControlBrowser.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControlBrowser.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.Panel2;
            this.splitContainerControlBrowser.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControlBrowser.Name = "splitContainerControlBrowser";
            //
            // splitContainerControlBrowser.Panel1
            //
            this.splitContainerControlBrowser.Panel1.Controls.Add(this.picBoxEbayLogo);
            this.splitContainerControlBrowser.Panel1.Controls.Add(this.linkeBayPrivacyPolicy);
            this.splitContainerControlBrowser.Panel1.Controls.Add(this.linkeBayUserAgreement);
            this.splitContainerControlBrowser.Panel1.Controls.Add(this.linkReportItem);
            this.splitContainerControlBrowser.Panel1.Controls.Add(this.webBrowser1);
            this.splitContainerControlBrowser.Panel1.Text = "Panel1";
            //
            // splitContainerControlBrowser.Panel2
            //
            this.splitContainerControlBrowser.Panel2.Controls.Add(this.zoomTrackBarBrowser);
            this.splitContainerControlBrowser.Panel2.Controls.Add(this.colorPickBrowser);
            this.splitContainerControlBrowser.Panel2.Controls.Add(this.btnHighlightWords);
            this.splitContainerControlBrowser.Panel2.MinSize = 60;
            this.splitContainerControlBrowser.Panel2.Text = "Panel2";
            this.splitContainerControlBrowser.Size = new System.Drawing.Size(1048, 211);
            this.splitContainerControlBrowser.SplitterPosition = 60;
            this.splitContainerControlBrowser.TabIndex = 33;
            //
            // linkeBayPrivacyPolicy
            //
            this.linkeBayPrivacyPolicy.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.linkeBayPrivacyPolicy.Cursor = System.Windows.Forms.Cursors.Hand;
            this.linkeBayPrivacyPolicy.Location = new System.Drawing.Point(3, 146);
            this.linkeBayPrivacyPolicy.Name = "linkeBayPrivacyPolicy";
            this.linkeBayPrivacyPolicy.Size = new System.Drawing.Size(92, 13);
            this.linkeBayPrivacyPolicy.TabIndex = 30;
            this.linkeBayPrivacyPolicy.Tag = "https://pages.ebay.com/help/policies/privacy-policy.html";
            this.linkeBayPrivacyPolicy.Text = "eBay Privacy Policy";
            this.linkeBayPrivacyPolicy.Visible = false;
            this.linkeBayPrivacyPolicy.HyperlinkClick += new DevExpress.Utils.HyperlinkClickEventHandler(this.hyperLinkLabel_Click);
            //
            // linkeBayUserAgreement
            //
            this.linkeBayUserAgreement.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.linkeBayUserAgreement.Cursor = System.Windows.Forms.Cursors.Hand;
            this.linkeBayUserAgreement.Location = new System.Drawing.Point(3, 164);
            this.linkeBayUserAgreement.Name = "linkeBayUserAgreement";
            this.linkeBayUserAgreement.Size = new System.Drawing.Size(105, 13);
            this.linkeBayUserAgreement.TabIndex = 29;
            this.linkeBayUserAgreement.Tag = "https://pages.ebay.com/help/policies/user-agreement.html";
            this.linkeBayUserAgreement.Text = "eBay User Agreement";
            this.linkeBayUserAgreement.Visible = false;
            this.linkeBayUserAgreement.Click += new System.EventHandler(this.hyperLinkLabel_Click);
            //
            // linkReportItem
            //
            this.linkReportItem.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.linkReportItem.Cursor = System.Windows.Forms.Cursors.Hand;
            this.linkReportItem.Location = new System.Drawing.Point(3, 181);
            this.linkReportItem.Name = "linkReportItem";
            this.linkReportItem.Size = new System.Drawing.Size(56, 13);
            this.linkReportItem.TabIndex = 29;
            this.linkReportItem.Text = "Report item";
            this.linkReportItem.Visible = false;
            this.linkReportItem.Click += new System.EventHandler(this.hyperLinkLabel_Click);
            //
            // zoomTrackBarBrowser
            //
            this.zoomTrackBarBrowser.EditValue = 100;
            this.zoomTrackBarBrowser.Location = new System.Drawing.Point(14, 100);
            this.zoomTrackBarBrowser.MenuManager = this.ribbonControl1;
            this.zoomTrackBarBrowser.Name = "zoomTrackBarBrowser";
            this.zoomTrackBarBrowser.Properties.AllowFocused = false;
            this.zoomTrackBarBrowser.Properties.AutoSize = false;
            this.zoomTrackBarBrowser.Properties.LargeChange = 10;
            this.zoomTrackBarBrowser.Properties.Maximum = 400;
            this.zoomTrackBarBrowser.Properties.Middle = 205;
            this.zoomTrackBarBrowser.Properties.Minimum = 10;
            this.zoomTrackBarBrowser.Properties.Orientation = System.Windows.Forms.Orientation.Vertical;
            this.zoomTrackBarBrowser.Properties.SmallChange = 10;
            this.zoomTrackBarBrowser.Properties.SnapToMiddle = 10;
            this.zoomTrackBarBrowser.Size = new System.Drawing.Size(29, 119);
            this.zoomTrackBarBrowser.TabIndex = 27;
            this.zoomTrackBarBrowser.ToolTip = "Change description font size";
            this.zoomTrackBarBrowser.Value = 100;
            this.zoomTrackBarBrowser.EditValueChanged += new System.EventHandler(this.zoomTrackBarBrowser_EditValueChanged);
            //
            // colorPickBrowser
            //
            this.colorPickBrowser.EditValue = System.Drawing.Color.Empty;
            this.colorPickBrowser.Location = new System.Drawing.Point(6, 57);
            this.colorPickBrowser.MenuManager = this.ribbonControl1;
            this.colorPickBrowser.Name = "colorPickBrowser";
            this.colorPickBrowser.Properties.AllowFocused = false;
            this.colorPickBrowser.Properties.AutoHeight = false;
            this.colorPickBrowser.Properties.AutomaticColor = System.Drawing.Color.Black;
            this.colorPickBrowser.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorPickBrowser.Properties.ShowWebSafeColors = true;
            this.colorPickBrowser.Size = new System.Drawing.Size(44, 24);
            this.colorPickBrowser.TabIndex = 28;
            this.colorPickBrowser.ToolTip = "Change description background color";
            this.colorPickBrowser.EditValueChanged += new System.EventHandler(this.colorPickBrowser_EditValueChanged);
            //
            // btnHighlightWords
            //
            this.btnHighlightWords.AllowFocus = false;
            this.btnHighlightWords.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.btnHighlightWords.Appearance.ForeColor = System.Drawing.Color.Transparent;
            this.btnHighlightWords.Appearance.Options.UseBackColor = true;
            this.btnHighlightWords.Appearance.Options.UseForeColor = true;
            this.btnHighlightWords.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.UltraFlat;
            this.btnHighlightWords.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Highlight;
            this.btnHighlightWords.ImageOptions.SvgImageSize = new System.Drawing.Size(25, 25);
            this.btnHighlightWords.Location = new System.Drawing.Point(14, 10);
            this.btnHighlightWords.Name = "btnHighlightWords";
            this.btnHighlightWords.Size = new System.Drawing.Size(28, 28);
            this.btnHighlightWords.TabIndex = 31;
            this.btnHighlightWords.ToolTip = "Highlight words in description";
            this.btnHighlightWords.Click += new System.EventHandler(this.btnHighlightWords_Click);
            //
            // flyoutPanelBrowser
            //
            this.flyoutPanelBrowser.Controls.Add(this.flyoutPanelControl1);
            this.flyoutPanelBrowser.Location = new System.Drawing.Point(566, 180);
            this.flyoutPanelBrowser.Name = "flyoutPanelBrowser";
            this.flyoutPanelBrowser.Options.AnchorType = DevExpress.Utils.Win.PopupToolWindowAnchor.Manual;
            this.flyoutPanelBrowser.Options.CloseOnOuterClick = true;
            this.flyoutPanelBrowser.Options.Location = new System.Drawing.Point(-327, -5);
            this.flyoutPanelBrowser.OwnerControl = this.btnBrowserSettings;
            this.flyoutPanelBrowser.Size = new System.Drawing.Size(316, 36);
            this.flyoutPanelBrowser.TabIndex = 32;
            //
            // flyoutPanelControl1
            //
            this.flyoutPanelControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.flyoutPanelControl1.FlyoutPanel = this.flyoutPanelBrowser;
            this.flyoutPanelControl1.Location = new System.Drawing.Point(0, 0);
            this.flyoutPanelControl1.Name = "flyoutPanelControl1";
            this.flyoutPanelControl1.Size = new System.Drawing.Size(316, 36);
            this.flyoutPanelControl1.TabIndex = 0;
            //
            // btnBrowserSettings
            //
            this.btnBrowserSettings.AllowFocus = false;
            this.btnBrowserSettings.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnBrowserSettings.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.UltraFlat;
            this.btnBrowserSettings.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Settings;
            this.btnBrowserSettings.ImageOptions.SvgImageSize = new System.Drawing.Size(20, 20);
            this.btnBrowserSettings.Location = new System.Drawing.Point(1191, 196);
            this.btnBrowserSettings.Name = "btnBrowserSettings";
            this.btnBrowserSettings.Size = new System.Drawing.Size(46, 50);
            this.btnBrowserSettings.TabIndex = 31;
            this.btnBrowserSettings.Visible = false;
            this.btnBrowserSettings.Click += new System.EventHandler(this.btnBrowserSettings_Click);
            //
            // dockLog
            //
            this.dockLog.Controls.Add(this.dockPanel7_Container);
            this.dockLog.DockedAsTabbedDocument = true;
            this.dockLog.FloatLocation = new System.Drawing.Point(675, 354);
            this.dockLog.FloatSize = new System.Drawing.Size(1556, 77);
            this.dockLog.Header = "Log";
            this.dockLog.ID = new System.Guid("2673e707-078d-4275-9043-425027488ef2");
            this.dockLog.Name = "dockLog";
            this.dockLog.OriginalSize = new System.Drawing.Size(1685, 77);
            this.dockLog.SavedIndex = 5;
            this.dockLog.SavedMdiDocument = true;
            this.dockLog.Text = "Log";
            //
            // dockPanel7_Container
            //
            this.dockPanel7_Container.Controls.Add(this.xtraTabLog);
            this.dockPanel7_Container.Location = new System.Drawing.Point(0, 0);
            this.dockPanel7_Container.Name = "dockPanel7_Container";
            this.dockPanel7_Container.Size = new System.Drawing.Size(1048, 211);
            this.dockPanel7_Container.TabIndex = 0;
            //
            // xtraTabLog
            //
            this.xtraTabLog.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabLog.Location = new System.Drawing.Point(0, 0);
            this.xtraTabLog.Name = "xtraTabLog";
            this.xtraTabLog.SelectedTabPage = this.xtraTabPageFilterLog;
            this.xtraTabLog.Size = new System.Drawing.Size(1048, 211);
            this.xtraTabLog.TabIndex = 96;
            this.xtraTabLog.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPageFilterLog,
            this.xtraTabPageErrorLog});
            //
            // xtraTabPageFilterLog
            //
            this.xtraTabPageFilterLog.Controls.Add(this.panelControlFilterLog);
            this.xtraTabPageFilterLog.Controls.Add(this.memoEditFilterLog);
            this.xtraTabPageFilterLog.Name = "xtraTabPageFilterLog";
            this.xtraTabPageFilterLog.Size = new System.Drawing.Size(1046, 186);
            this.xtraTabPageFilterLog.Text = "Filter Log";
            //
            // panelControlFilterLog
            //
            this.panelControlFilterLog.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.panelControlFilterLog.Controls.Add(this.btnClearFilterLog);
            this.panelControlFilterLog.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelControlFilterLog.Location = new System.Drawing.Point(0, 0);
            this.panelControlFilterLog.MaximumSize = new System.Drawing.Size(0, 28);
            this.panelControlFilterLog.Name = "panelControlFilterLog";
            this.panelControlFilterLog.Size = new System.Drawing.Size(1046, 28);
            this.panelControlFilterLog.TabIndex = 96;
            //
            // btnClearFilterLog
            //
            this.btnClearFilterLog.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("btnClearFilterLog.ImageOptions.SvgImage")));
            this.btnClearFilterLog.ImageOptions.SvgImageSize = new System.Drawing.Size(20, 20);
            this.btnClearFilterLog.Location = new System.Drawing.Point(0, 2);
            this.btnClearFilterLog.Name = "btnClearFilterLog";
            this.btnClearFilterLog.Size = new System.Drawing.Size(24, 23);
            this.btnClearFilterLog.TabIndex = 93;
            this.btnClearFilterLog.Click += new System.EventHandler(this.btnClearFilterLog_Click);
            //
            // memoEditFilterLog
            //
            this.memoEditFilterLog.Dock = System.Windows.Forms.DockStyle.Fill;
            this.memoEditFilterLog.Location = new System.Drawing.Point(0, 0);
            this.memoEditFilterLog.Name = "memoEditFilterLog";
            this.memoEditFilterLog.Size = new System.Drawing.Size(1046, 186);
            this.memoEditFilterLog.TabIndex = 92;
            //
            // xtraTabPageErrorLog
            //
            this.xtraTabPageErrorLog.Controls.Add(this.memoEditErrorLog);
            this.xtraTabPageErrorLog.Controls.Add(this.panelControlErrorLog);
            this.xtraTabPageErrorLog.Name = "xtraTabPageErrorLog";
            this.xtraTabPageErrorLog.Size = new System.Drawing.Size(1046, 186);
            this.xtraTabPageErrorLog.Text = "Error Log";
            //
            // memoEditErrorLog
            //
            this.memoEditErrorLog.Dock = System.Windows.Forms.DockStyle.Fill;
            this.memoEditErrorLog.Location = new System.Drawing.Point(0, 28);
            this.memoEditErrorLog.MenuManager = this.ribbonControl1;
            this.memoEditErrorLog.Name = "memoEditErrorLog";
            this.memoEditErrorLog.Size = new System.Drawing.Size(1046, 158);
            this.memoEditErrorLog.TabIndex = 92;
            //
            // panelControlErrorLog
            //
            this.panelControlErrorLog.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.panelControlErrorLog.Controls.Add(this.hyperlinkLabelOpoenLogFolder);
            this.panelControlErrorLog.Controls.Add(this.btnClearLogs);
            this.panelControlErrorLog.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelControlErrorLog.Location = new System.Drawing.Point(0, 0);
            this.panelControlErrorLog.MaximumSize = new System.Drawing.Size(0, 28);
            this.panelControlErrorLog.Name = "panelControlErrorLog";
            this.panelControlErrorLog.Size = new System.Drawing.Size(0, 28);
            this.panelControlErrorLog.TabIndex = 95;
            //
            // hyperlinkLabelOpoenLogFolder
            //
            this.hyperlinkLabelOpoenLogFolder.Cursor = System.Windows.Forms.Cursors.Hand;
            this.hyperlinkLabelOpoenLogFolder.Location = new System.Drawing.Point(43, 9);
            this.hyperlinkLabelOpoenLogFolder.Name = "hyperlinkLabelOpoenLogFolder";
            this.hyperlinkLabelOpoenLogFolder.Size = new System.Drawing.Size(43, 13);
            this.hyperlinkLabelOpoenLogFolder.TabIndex = 94;
            this.hyperlinkLabelOpoenLogFolder.Text = "_______";
            this.hyperlinkLabelOpoenLogFolder.Click += new System.EventHandler(this.hyperlinkLabelControl1_Click);
            //
            // btnClearLogs
            //
            this.btnClearLogs.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("btnClearLogs.ImageOptions.SvgImage")));
            this.btnClearLogs.ImageOptions.SvgImageSize = new System.Drawing.Size(20, 20);
            this.btnClearLogs.Location = new System.Drawing.Point(0, 2);
            this.btnClearLogs.Name = "btnClearLogs";
            this.btnClearLogs.Size = new System.Drawing.Size(24, 23);
            this.btnClearLogs.TabIndex = 93;
            this.btnClearLogs.ToolTip = "Clear log";
            this.btnClearLogs.Click += new System.EventHandler(this.btnClearLogs_Click);
            //
            // dockPictures
            //
            this.dockPictures.Controls.Add(this.controlContainer6);
            this.dockPictures.DockedAsTabbedDocument = true;
            this.dockPictures.FloatLocation = new System.Drawing.Point(533, 592);
            this.dockPictures.FloatSize = new System.Drawing.Size(758, 216);
            this.dockPictures.Header = "Pictures";
            this.dockPictures.ID = new System.Guid("2daad929-1369-41cd-b209-81249f93343e");
            this.dockPictures.Name = "dockPictures";
            this.dockPictures.OriginalSize = new System.Drawing.Size(200, 144);
            this.dockPictures.SavedIndex = 6;
            this.dockPictures.SavedMdiDocument = true;
            this.dockPictures.Text = "Pictures";
            //
            // controlContainer6
            //
            this.controlContainer6.Controls.Add(this.layoutControlForPicture);
            this.controlContainer6.Location = new System.Drawing.Point(0, 0);
            this.controlContainer6.Name = "controlContainer6";
            this.controlContainer6.Size = new System.Drawing.Size(1048, 171);
            this.controlContainer6.TabIndex = 0;
            //
            // dockPanelBuy
            //
            this.dockPanelBuy.Controls.Add(this.controlContainer4);
            this.dockPanelBuy.DockedAsTabbedDocument = true;
            this.dockPanelBuy.FloatLocation = new System.Drawing.Point(681, 576);
            this.dockPanelBuy.FloatSize = new System.Drawing.Size(65, 77);
            this.dockPanelBuy.Header = "Buy";
            this.dockPanelBuy.ID = new System.Guid("643a62b5-fa08-4ea0-840b-6b0058b23ad7");
            this.dockPanelBuy.Name = "dockPanelBuy";
            this.dockPanelBuy.OriginalSize = new System.Drawing.Size(504, 200);
            this.dockPanelBuy.SavedIndex = 6;
            this.dockPanelBuy.SavedMdiDocument = true;
            this.dockPanelBuy.SavedMdiDocumentIndex = 5;
            //
            // controlContainer4
            //
            this.controlContainer4.Controls.Add(this.splitContainerBuyOffer);
            this.controlContainer4.Location = new System.Drawing.Point(0, 0);
            this.controlContainer4.Margin = new System.Windows.Forms.Padding(2);
            this.controlContainer4.Name = "controlContainer4";
            this.controlContainer4.Size = new System.Drawing.Size(1048, 211);
            this.controlContainer4.TabIndex = 0;
            //
            // splitContainerBuyOffer
            //
            this.splitContainerBuyOffer.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel2;
            this.splitContainerBuyOffer.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.splitContainerBuyOffer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerBuyOffer.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerBuyOffer.Location = new System.Drawing.Point(0, 0);
            this.splitContainerBuyOffer.Margin = new System.Windows.Forms.Padding(2);
            this.splitContainerBuyOffer.Name = "splitContainerBuyOffer";
            //
            // splitContainerBuyOffer.Panel1
            //
            this.splitContainerBuyOffer.Panel1.Controls.Add(this.panelBuyButton);
            this.splitContainerBuyOffer.Panel1.Text = "Panel1";
            //
            // splitContainerBuyOffer.Panel2
            //
            this.splitContainerBuyOffer.Panel2.Controls.Add(this.btnMakeOffer);
            this.splitContainerBuyOffer.Panel2.Text = "Panel2";
            this.splitContainerBuyOffer.Size = new System.Drawing.Size(1048, 211);
            this.splitContainerBuyOffer.SplitterPosition = 540;
            this.splitContainerBuyOffer.TabIndex = 1;
            this.splitContainerBuyOffer.Text = "splitContainerControl1";
            //
            // panelBuyButton
            //
            this.panelBuyButton.Appearance.Font = new System.Drawing.Font("Tahoma", 18.25F);
            this.panelBuyButton.Appearance.Options.UseFont = true;
            this.panelBuyButton.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelBuyButton.Location = new System.Drawing.Point(0, 0);
            this.panelBuyButton.Margin = new System.Windows.Forms.Padding(2);
            this.panelBuyButton.Name = "panelBuyButton";
            this.panelBuyButton.Size = new System.Drawing.Size(540, 211);
            this.panelBuyButton.TabIndex = 0;
            this.panelBuyButton.Text = "Buy";
            this.panelBuyButton.Click += new System.EventHandler(this.panelBuyButton_Click_1);
            //
            // btnMakeOffer
            //
            this.btnMakeOffer.Appearance.Font = new System.Drawing.Font("Tahoma", 18.25F);
            this.btnMakeOffer.Appearance.Options.UseFont = true;
            this.btnMakeOffer.Appearance.Options.UseTextOptions = true;
            this.btnMakeOffer.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.btnMakeOffer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnMakeOffer.Location = new System.Drawing.Point(0, 0);
            this.btnMakeOffer.Margin = new System.Windows.Forms.Padding(2);
            this.btnMakeOffer.Name = "btnMakeOffer";
            this.btnMakeOffer.Size = new System.Drawing.Size(498, 211);
            this.btnMakeOffer.TabIndex = 0;
            this.btnMakeOffer.Text = "Make Offer";
            this.btnMakeOffer.Click += new System.EventHandler(this.btnMakeOffer_Click);
            //
            // dockPanelExternalData
            //
            this.dockPanelExternalData.Controls.Add(this.controlContainer3);
            this.dockPanelExternalData.DockedAsTabbedDocument = true;
            this.dockPanelExternalData.FloatLocation = new System.Drawing.Point(655, 364);
            this.dockPanelExternalData.ID = new System.Guid("bc9b5a5a-3146-460d-a172-3c07d279d526");
            this.dockPanelExternalData.Name = "dockPanelExternalData";
            this.dockPanelExternalData.OriginalSize = new System.Drawing.Size(200, 200);
            this.dockPanelExternalData.Text = "External Data";
            //
            // controlContainer3
            //
            this.controlContainer3.Controls.Add(this.layoutControlCefBrowser);
            this.controlContainer3.Location = new System.Drawing.Point(0, 0);
            this.controlContainer3.Name = "controlContainer3";
            this.controlContainer3.Size = new System.Drawing.Size(1048, 211);
            this.controlContainer3.TabIndex = 0;
            //
            // layoutControlCefBrowser
            //
            this.layoutControlCefBrowser.Controls.Add(this.panelCefBrowser);
            this.layoutControlCefBrowser.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutControlCefBrowser.Location = new System.Drawing.Point(0, 0);
            this.layoutControlCefBrowser.Name = "layoutControlCefBrowser";
            this.layoutControlCefBrowser.Root = this.layoutControlGroupExternalData;
            this.layoutControlCefBrowser.Size = new System.Drawing.Size(1048, 211);
            this.layoutControlCefBrowser.TabIndex = 1;
            this.layoutControlCefBrowser.Text = "layoutControl2";
            //
            // panelCefBrowser
            //
            this.panelCefBrowser.Location = new System.Drawing.Point(2, 2);
            this.panelCefBrowser.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.panelCefBrowser.Name = "panelCefBrowser";
            this.panelCefBrowser.Size = new System.Drawing.Size(1044, 207);
            this.panelCefBrowser.TabIndex = 0;
            //
            // layoutControlGroupExternalData
            //
            this.layoutControlGroupExternalData.CustomizationFormText = "layoutControlGroupExternalData";
            this.layoutControlGroupExternalData.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.layoutControlGroupExternalData.GroupBordersVisible = false;
            this.layoutControlGroupExternalData.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem2});
            this.layoutControlGroupExternalData.Name = "layoutControlGroupExternalData";
            this.layoutControlGroupExternalData.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.layoutControlGroupExternalData.Size = new System.Drawing.Size(1048, 211);
            this.layoutControlGroupExternalData.TextVisible = false;
            //
            // layoutControlItem2
            //
            this.layoutControlItem2.Control = this.panelCefBrowser;
            this.layoutControlItem2.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem2.Name = "layoutControlItem2";
            this.layoutControlItem2.Size = new System.Drawing.Size(1048, 211);
            this.layoutControlItem2.TextVisible = false;
            //
            // dockPanelWatchlist
            //
            this.dockPanelWatchlist.Controls.Add(this.controlContainer7);
            this.dockPanelWatchlist.DockedAsTabbedDocument = true;
            this.dockPanelWatchlist.FloatLocation = new System.Drawing.Point(1611, 606);
            this.dockPanelWatchlist.ID = new System.Guid("e2b4746e-3cb9-4199-acba-f72a32aca2f4");
            this.dockPanelWatchlist.Name = "dockPanelWatchlist";
            this.dockPanelWatchlist.OriginalSize = new System.Drawing.Size(200, 200);
            this.dockPanelWatchlist.SavedIndex = 10;
            this.dockPanelWatchlist.SavedMdiDocument = true;
            this.dockPanelWatchlist.SavedMdiDocumentIndex = 0;
            this.dockPanelWatchlist.Text = "Watchlist";
            //
            // controlContainer7
            //
            this.controlContainer7.Controls.Add(this.layoutWatchlistControls);
            this.controlContainer7.Dock = System.Windows.Forms.DockStyle.Fill;
            this.controlContainer7.Location = new System.Drawing.Point(0, 0);
            this.controlContainer7.Name = "controlContainer7";
            this.controlContainer7.Size = new System.Drawing.Size(1048, 171);
            this.controlContainer7.TabIndex = 0;
            //
            // layoutWatchlistControls
            //
            this.layoutWatchlistControls.Controls.Add(this.lnkWatchlistImportFromClipboard);
            this.layoutWatchlistControls.Controls.Add(this.chkRefreshAndNotifyWatchlist);
            this.layoutWatchlistControls.Controls.Add(this.btnRefreshWatchlist);
            this.layoutWatchlistControls.Controls.Add(this.timeSpanWatchlistRefreshInterval);
            this.layoutWatchlistControls.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutWatchlistControls.LayoutVersion = "1";
            this.layoutWatchlistControls.Location = new System.Drawing.Point(0, 0);
            this.layoutWatchlistControls.Name = "layoutWatchlistControls";
            this.layoutWatchlistControls.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = new System.Drawing.Rectangle(750, 766, 695, 669);
            this.layoutWatchlistControls.Root = this.layoutWatchlistRoot;
            this.layoutWatchlistControls.Size = new System.Drawing.Size(1048, 171);
            this.layoutWatchlistControls.TabIndex = 0;
            this.layoutWatchlistControls.LayoutUpgrade += new DevExpress.Utils.LayoutUpgradeEventHandler(this.layoutWatchlistControls_LayoutUpgrade);
            //
            // lnkWatchlistImportFromClipboard
            //
            this.lnkWatchlistImportFromClipboard.Anchor = System.Windows.Forms.AnchorStyles.Right;
            this.lnkWatchlistImportFromClipboard.Location = new System.Drawing.Point(943, 156);
            this.lnkWatchlistImportFromClipboard.Name = "lnkWatchlistImportFromClipboard";
            this.lnkWatchlistImportFromClipboard.Size = new System.Drawing.Size(103, 13);
            this.lnkWatchlistImportFromClipboard.StyleController = this.layoutWatchlistControls;
            this.lnkWatchlistImportFromClipboard.TabIndex = 1;
            this.lnkWatchlistImportFromClipboard.Text = "Import from clipboard";
            this.lnkWatchlistImportFromClipboard.ToolTip = "Import item ids from clipboard. Item ids must be separated by comma or a new line" +
    ".\r\nEx. 205411290493,205411290494,205411290495\r\nor\r\n205411290493\r\n205411290494\r\n2" +
    "05411290495";
            this.lnkWatchlistImportFromClipboard.Click += new System.EventHandler(this.lnkWatchlistImportFromClipboard_Click);
            //
            // chkRefreshAndNotifyWatchlist
            //
            this.chkRefreshAndNotifyWatchlist.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.chkRefreshAndNotifyWatchlist.Location = new System.Drawing.Point(905, 38);
            this.chkRefreshAndNotifyWatchlist.MenuManager = this.ribbonControl1;
            this.chkRefreshAndNotifyWatchlist.Name = "chkRefreshAndNotifyWatchlist";
            this.chkRefreshAndNotifyWatchlist.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chkRefreshAndNotifyWatchlist.Properties.OffText = "Auto refresh Off";
            this.chkRefreshAndNotifyWatchlist.Properties.OnText = "Auto refresh";
            this.chkRefreshAndNotifyWatchlist.Size = new System.Drawing.Size(141, 18);
            this.chkRefreshAndNotifyWatchlist.StyleController = this.layoutWatchlistControls;
            this.chkRefreshAndNotifyWatchlist.TabIndex = 2;
            this.chkRefreshAndNotifyWatchlist.EditValueChanged += new System.EventHandler(this.chkRefreshAndNotify_EditValueChanged);
            //
            // btnRefreshWatchlist
            //
            this.btnRefreshWatchlist.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRefreshWatchlist.Location = new System.Drawing.Point(905, 2);
            this.btnRefreshWatchlist.Name = "btnRefreshWatchlist";
            this.btnRefreshWatchlist.Padding = new System.Windows.Forms.Padding(0, 0, 9, 0);
            this.btnRefreshWatchlist.Size = new System.Drawing.Size(141, 22);
            this.btnRefreshWatchlist.StyleController = this.layoutWatchlistControls;
            this.btnRefreshWatchlist.TabIndex = 0;
            this.btnRefreshWatchlist.Text = "Refresh";
            this.btnRefreshWatchlist.Click += new System.EventHandler(this.btnRefreshWatchlist_Click);
            //
            // timeSpanWatchlistRefreshInterval
            //
            this.timeSpanWatchlistRefreshInterval.EditValue = System.TimeSpan.Parse("00:05:00");
            this.timeSpanWatchlistRefreshInterval.Location = new System.Drawing.Point(996, 60);
            this.timeSpanWatchlistRefreshInterval.MenuManager = this.ribbonControl1;
            this.timeSpanWatchlistRefreshInterval.Name = "timeSpanWatchlistRefreshInterval";
            this.timeSpanWatchlistRefreshInterval.Properties.AllowFocused = false;
            this.timeSpanWatchlistRefreshInterval.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.timeSpanWatchlistRefreshInterval.Properties.DisplayFormat.FormatString = "[hh\\h][:mm\\m][:ss\\s]";
            this.timeSpanWatchlistRefreshInterval.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.timeSpanWatchlistRefreshInterval.Properties.MaskSettings.Set("mask", "[h\\h]  [m\\m]  [s\\s]");
            this.timeSpanWatchlistRefreshInterval.Properties.MaskSettings.Set("defaultPart", DevExpress.Data.Mask.TimeSpanMaskPart.Minutes);
            this.timeSpanWatchlistRefreshInterval.Properties.MaskSettings.Set("allowNegativeValues", false);
            this.timeSpanWatchlistRefreshInterval.Properties.MinValue = System.TimeSpan.Parse("00:00:10");
            this.timeSpanWatchlistRefreshInterval.Properties.UseMaskAsDisplayFormat = true;
            this.timeSpanWatchlistRefreshInterval.Size = new System.Drawing.Size(50, 20);
            this.timeSpanWatchlistRefreshInterval.StyleController = this.layoutWatchlistControls;
            this.timeSpanWatchlistRefreshInterval.TabIndex = 3;
            this.timeSpanWatchlistRefreshInterval.EditValueChanged += new System.EventHandler(this.timeSpanWatchlistRefreshInterval_EditValueChanged);
            //
            // layoutWatchlistRoot
            //
            this.layoutWatchlistRoot.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.layoutWatchlistRoot.GroupBordersVisible = false;
            this.layoutWatchlistRoot.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.lciWatchlist,
            this.layoutControlItem3,
            this.lciWatchlistRefreshInterval,
            this.layoutControlItem4,
            this.emptySpaceItem3,
            this.splitterItem1,
            this.splitterItem2,
            this.layoutControlItem5});
            this.layoutWatchlistRoot.Name = "Root";
            this.layoutWatchlistRoot.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.layoutWatchlistRoot.Size = new System.Drawing.Size(1048, 171);
            this.layoutWatchlistRoot.TextVisible = false;
            //
            // lciWatchlist
            //
            this.lciWatchlist.BestFitWeight = 1000;
            this.lciWatchlist.Location = new System.Drawing.Point(0, 0);
            this.lciWatchlist.Name = "lciWatchlist";
            this.lciWatchlist.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.lciWatchlist.Size = new System.Drawing.Size(893, 171);
            this.lciWatchlist.TextVisible = false;
            //
            // layoutControlItem3
            //
            this.layoutControlItem3.BestFitWeight = 1;
            this.layoutControlItem3.Control = this.btnRefreshWatchlist;
            this.layoutControlItem3.Location = new System.Drawing.Point(903, 0);
            this.layoutControlItem3.Name = "layoutControlItem3";
            this.layoutControlItem3.Size = new System.Drawing.Size(145, 26);
            this.layoutControlItem3.TextVisible = false;
            //
            // lciWatchlistRefreshInterval
            //
            this.lciWatchlistRefreshInterval.BestFitWeight = 1;
            this.lciWatchlistRefreshInterval.ContentHorzAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lciWatchlistRefreshInterval.Control = this.timeSpanWatchlistRefreshInterval;
            this.lciWatchlistRefreshInterval.CustomizationFormText = "lciWatchlistRefreshInterval";
            this.lciWatchlistRefreshInterval.Location = new System.Drawing.Point(903, 58);
            this.lciWatchlistRefreshInterval.Name = "lciWatchlistRefreshInterval";
            this.lciWatchlistRefreshInterval.Size = new System.Drawing.Size(145, 24);
            this.lciWatchlistRefreshInterval.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.SupportHorzAlignment;
            this.lciWatchlistRefreshInterval.Text = "Refresh Interval";
            this.lciWatchlistRefreshInterval.TextSize = new System.Drawing.Size(79, 13);
            //
            // layoutControlItem4
            //
            this.layoutControlItem4.BestFitWeight = 1;
            this.layoutControlItem4.ContentHorzAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.layoutControlItem4.Control = this.chkRefreshAndNotifyWatchlist;
            this.layoutControlItem4.Location = new System.Drawing.Point(903, 36);
            this.layoutControlItem4.Name = "layoutControlItem4";
            this.layoutControlItem4.Size = new System.Drawing.Size(145, 22);
            this.layoutControlItem4.TextVisible = false;
            //
            // emptySpaceItem3
            //
            this.emptySpaceItem3.Location = new System.Drawing.Point(903, 82);
            this.emptySpaceItem3.Name = "emptySpaceItem3";
            this.emptySpaceItem3.Size = new System.Drawing.Size(145, 72);
            //
            // splitterItem1
            //
            this.splitterItem1.Location = new System.Drawing.Point(893, 0);
            this.splitterItem1.Name = "splitterItem1";
            this.splitterItem1.Size = new System.Drawing.Size(10, 154);
            //
            // splitterItem2
            //
            this.splitterItem2.Location = new System.Drawing.Point(903, 26);
            this.splitterItem2.Name = "splitterItem2";
            this.splitterItem2.Size = new System.Drawing.Size(145, 10);
            //
            // layoutControlItem5
            //
            this.layoutControlItem5.ContentHorzAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.layoutControlItem5.Control = this.lnkWatchlistImportFromClipboard;
            this.layoutControlItem5.Location = new System.Drawing.Point(893, 154);
            this.layoutControlItem5.Name = "layoutControlItem5";
            this.layoutControlItem5.Size = new System.Drawing.Size(155, 17);
            this.layoutControlItem5.TextVisible = false;
            //
            // dockMainGrid
            //
            this.dockMainGrid.Controls.Add(this.dockPanel6_Container);
            this.dockMainGrid.DockedAsTabbedDocument = true;
            this.dockMainGrid.FloatLocation = new System.Drawing.Point(1509, 360);
            this.dockMainGrid.FloatSize = new System.Drawing.Size(593, 265);
            this.dockMainGrid.FloatVertical = true;
            this.dockMainGrid.Header = "Results1";
            this.dockMainGrid.ID = new System.Guid("92581858-ecdc-4d88-be24-9bb7948442dc");
            this.dockMainGrid.Name = "dockMainGrid";
            this.dockMainGrid.OriginalSize = new System.Drawing.Size(1380, 170);
            this.dockMainGrid.SavedIndex = 6;
            this.dockMainGrid.SavedMdiDocument = true;
            this.dockMainGrid.Text = "Results1";
            //
            // dockPanel6_Container
            //
            this.dockPanel6_Container.Controls.Add(this.gridControl1);
            this.dockPanel6_Container.Location = new System.Drawing.Point(4, 38);
            this.dockPanel6_Container.Name = "dockPanel6_Container";
            this.dockPanel6_Container.Size = new System.Drawing.Size(585, 223);
            this.dockPanel6_Container.TabIndex = 0;
            //
            // ribbonControl2
            //
            this.ribbonControl2.AllowKeyTips = false;
            this.ribbonControl2.BackColor = System.Drawing.Color.Red;
            this.ribbonControl2.ExpandCollapseItem.Id = 0;
            this.ribbonControl2.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbonControl2.ExpandCollapseItem,
            this.barButtonItem1,
            this.barButtonItem2,
            this.barStaticItem1,
            this.barStaticItem3,
            this.barButtonItem3,
            this.barButtonItem4,
            this.barButtonItem5,
            this.barButtonItem6,
            this.barStaticItem4,
            this.barStaticItem5,
            this.barDockingMenuItem1,
            this.barHeaderItem3,
            this.barHeaderItem4,
            this.barStaticItem6,
            this.barButtonItem7});
            this.ribbonControl2.Location = new System.Drawing.Point(0, 0);
            this.ribbonControl2.MaxItemId = 2;
            this.ribbonControl2.Name = "ribbonControl2";
            this.ribbonControl2.Pages.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPage[] {
            this.ribbonPage2,
            this.ribbonPage3,
            this.ribbonPage5});
            this.ribbonControl2.QuickToolbarItemLinks.Add(this.barButtonItem3);
            this.ribbonControl2.QuickToolbarItemLinks.Add(this.barButtonItem4);
            this.ribbonControl2.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonControlStyle.Office2013;
            this.ribbonControl2.ShowApplicationButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl2.ShowExpandCollapseButton = DevExpress.Utils.DefaultBoolean.True;
            this.ribbonControl2.Size = new System.Drawing.Size(1030, 0);
            this.ribbonControl2.StatusBar = this.ribbonStatusBar3;
            //
            // barButtonItem1
            //
            this.barButtonItem1.Caption = "Clear results";
            this.barButtonItem1.Id = 114;
            this.barButtonItem1.Name = "barButtonItem1";
            toolTipItem22.Text = "Clear all listing data in all grids";
            superToolTip24.Items.Add(toolTipItem22);
            this.barButtonItem1.SuperTip = superToolTip24;
            //
            // barButtonItem2
            //
            this.barButtonItem2.Caption = "Support";
            this.barButtonItem2.Id = 72;
            this.barButtonItem2.ImageOptions.AllowGlyphSkinning = DevExpress.Utils.DefaultBoolean.False;
            this.barButtonItem2.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem2.ImageOptions.Image")));
            this.barButtonItem2.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem2.ImageOptions.LargeImage")));
            this.barButtonItem2.Name = "barButtonItem2";
            toolTipItem23.Text = "Report a bug, issue or request/vote on new features.";
            superToolTip25.Items.Add(toolTipItem23);
            this.barButtonItem2.SuperTip = superToolTip25;
            //
            // barStaticItem1
            //
            this.barStaticItem1.Caption = "License: Checking...";
            this.barStaticItem1.Id = 237;
            this.barStaticItem1.Name = "barStaticItem1";
            //
            // barStaticItem3
            //
            this.barStaticItem3.Caption = "0";
            this.barStaticItem3.Id = 353;
            this.barStaticItem3.Name = "barStaticItem3";
            //
            // barButtonItem3
            //
            this.barButtonItem3.Caption = "Loading...";
            this.barButtonItem3.Enabled = false;
            this.barButtonItem3.Id = 263;
            this.barButtonItem3.Name = "barButtonItem3";
            //
            // barButtonItem4
            //
            this.barButtonItem4.Caption = "Buy";
            this.barButtonItem4.Id = 571;
            this.barButtonItem4.Name = "barButtonItem4";
            //
            // barButtonItem5
            //
            this.barButtonItem5.Caption = "Subscription Info";
            this.barButtonItem5.Id = 115;
            this.barButtonItem5.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem5.ImageOptions.Image")));
            this.barButtonItem5.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem5.ImageOptions.LargeImage")));
            this.barButtonItem5.Name = "barButtonItem5";
            toolTipItem24.Text = "Signup, view or change your subscription plan.";
            superToolTip26.Items.Add(toolTipItem24);
            this.barButtonItem5.SuperTip = superToolTip26;
            //
            // barButtonItem6
            //
            this.barButtonItem6.Caption = "Terms and Conditions";
            this.barButtonItem6.Id = 82;
            this.barButtonItem6.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem6.ImageOptions.Image")));
            this.barButtonItem6.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem6.ImageOptions.LargeImage")));
            this.barButtonItem6.Name = "barButtonItem6";
            //
            // barStaticItem4
            //
            this.barStaticItem4.Caption = "Build: v.********";
            this.barStaticItem4.Id = 238;
            this.barStaticItem4.Name = "barStaticItem4";
            //
            // barStaticItem5
            //
            this.barStaticItem5.Caption = "0";
            this.barStaticItem5.Id = 364;
            this.barStaticItem5.Name = "barStaticItem5";
            //
            // barDockingMenuItem1
            //
            this.barDockingMenuItem1.Caption = "Docking";
            this.barDockingMenuItem1.Id = 451;
            this.barDockingMenuItem1.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barDockingMenuItem1.ImageOptions.Image")));
            this.barDockingMenuItem1.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barDockingMenuItem1.ImageOptions.LargeImage")));
            this.barDockingMenuItem1.Name = "barDockingMenuItem1";
            //
            // barHeaderItem3
            //
            this.barHeaderItem3.Caption = "                     ";
            this.barHeaderItem3.Id = 671;
            this.barHeaderItem3.Name = "barHeaderItem3";
            //
            // barHeaderItem4
            //
            this.barHeaderItem4.Caption = "                                                             ";
            this.barHeaderItem4.Id = 281;
            this.barHeaderItem4.Name = "barHeaderItem4";
            //
            // barStaticItem6
            //
            this.barStaticItem6.Id = 101;
            this.barStaticItem6.Name = "barStaticItem6";
            //
            // barButtonItem7
            //
            this.barButtonItem7.Caption = "Reset Layout";
            this.barButtonItem7.Id = 102;
            this.barButtonItem7.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem7.ImageOptions.Image")));
            this.barButtonItem7.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem7.ImageOptions.LargeImage")));
            this.barButtonItem7.Name = "barButtonItem7";
            toolTipItem25.Text = "Application panels and grid columns will be set to a default state.";
            superToolTip27.Items.Add(toolTipItem25);
            this.barButtonItem7.SuperTip = superToolTip27;
            //
            // ribbonPage2
            //
            this.ribbonPage2.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup6});
            this.ribbonPage2.Name = "ribbonPage2";
            this.ribbonPage2.Text = "Home";
            //
            // ribbonPageGroup6
            //
            this.ribbonPageGroup6.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroup6.ItemLinks.Add(this.barButtonItem3);
            this.ribbonPageGroup6.ItemLinks.Add(this.barButtonItem1);
            this.ribbonPageGroup6.ItemLinks.Add(this.barButtonItem4);
            this.ribbonPageGroup6.Name = "ribbonPageGroup6";
            //
            // ribbonPage3
            //
            this.ribbonPage3.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup7,
            this.ribbonPageGroup8});
            this.ribbonPage3.Name = "ribbonPage3";
            this.ribbonPage3.Text = "Options";
            //
            // ribbonPageGroup7
            //
            this.ribbonPageGroup7.Name = "ribbonPageGroup7";
            this.ribbonPageGroup7.Text = "Skin";
            //
            // ribbonPageGroup8
            //
            this.ribbonPageGroup8.ItemLinks.Add(this.barDockingMenuItem1);
            this.ribbonPageGroup8.ItemLinks.Add(this.barButtonItem7);
            this.ribbonPageGroup8.Name = "ribbonPageGroup8";
            this.ribbonPageGroup8.Text = "Layout";
            //
            // ribbonPage5
            //
            this.ribbonPage5.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup9,
            this.ribbonPageGroup10});
            this.ribbonPage5.Name = "ribbonPage5";
            this.ribbonPage5.Text = "Help";
            //
            // ribbonPageGroup9
            //
            this.ribbonPageGroup9.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroup9.ItemLinks.Add(this.barButtonItem5);
            this.ribbonPageGroup9.ItemLinks.Add(this.barButtonItem2);
            this.ribbonPageGroup9.ItemLinks.Add(this.barButtonItem6);
            this.ribbonPageGroup9.Name = "ribbonPageGroup9";
            //
            // ribbonPageGroup10
            //
            this.ribbonPageGroup10.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroup10.ItemLinks.Add(this.barStaticItem4);
            this.ribbonPageGroup10.Name = "ribbonPageGroup10";
            //
            // ribbonStatusBar3
            //
            this.ribbonStatusBar3.Location = new System.Drawing.Point(0, 146);
            this.ribbonStatusBar3.Name = "ribbonStatusBar3";
            this.ribbonStatusBar3.Ribbon = this.ribbonControl2;
            this.ribbonStatusBar3.Size = new System.Drawing.Size(979, 27);
            //
            // ribbonControl3
            //
            this.ribbonControl3.AllowKeyTips = false;
            this.ribbonControl3.BackColor = System.Drawing.Color.Red;
            this.ribbonControl3.ExpandCollapseItem.Id = 0;
            this.ribbonControl3.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbonControl3.ExpandCollapseItem,
            this.barButtonItem8,
            this.barButtonItem9,
            this.barStaticItem7,
            this.barButtonItem10,
            this.barButtonItem11,
            this.barButtonItem12,
            this.barButtonItem13,
            this.barStaticItem10,
            this.barStaticItem11,
            this.barDockingMenuItem2,
            this.barHeaderItem5,
            this.barHeaderItem6,
            this.barStaticItem12,
            this.barButtonItem14});
            this.ribbonControl3.Location = new System.Drawing.Point(0, 0);
            this.ribbonControl3.MaxItemId = 2;
            this.ribbonControl3.Name = "ribbonControl3";
            this.ribbonControl3.Pages.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPage[] {
            this.ribbonPage6,
            this.ribbonPage7,
            this.ribbonPage8});
            this.ribbonControl3.QuickToolbarItemLinks.Add(this.barButtonItem10);
            this.ribbonControl3.QuickToolbarItemLinks.Add(this.barButtonItem11);
            this.ribbonControl3.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonControlStyle.Office2013;
            this.ribbonControl3.ShowApplicationButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl3.ShowExpandCollapseButton = DevExpress.Utils.DefaultBoolean.True;
            this.ribbonControl3.Size = new System.Drawing.Size(1030, 0);
            this.ribbonControl3.StatusBar = this.ribbonStatusBar4;
            //
            // barButtonItem8
            //
            this.barButtonItem8.Caption = "Clear results";
            this.barButtonItem8.Id = 103;
            this.barButtonItem8.Name = "barButtonItem8";
            toolTipItem26.Text = "Clear all listing data in all grids";
            superToolTip28.Items.Add(toolTipItem26);
            this.barButtonItem8.SuperTip = superToolTip28;
            //
            // barButtonItem9
            //
            this.barButtonItem9.Caption = "Support";
            this.barButtonItem9.Id = 92;
            this.barButtonItem9.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem9.ImageOptions.Image")));
            this.barButtonItem9.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem9.ImageOptions.LargeImage")));
            this.barButtonItem9.Name = "barButtonItem9";
            toolTipItem27.Text = "Report a bug, issue or request/vote on new features.";
            superToolTip29.Items.Add(toolTipItem27);
            this.barButtonItem9.SuperTip = superToolTip29;
            //
            // barStaticItem7
            //
            this.barStaticItem7.Caption = "License: Checking...";
            this.barStaticItem7.Id = 239;
            this.barStaticItem7.Name = "barStaticItem7";
            //
            // barButtonItem10
            //
            this.barButtonItem10.Caption = "Loading...";
            this.barButtonItem10.Enabled = false;
            this.barButtonItem10.Id = 264;
            this.barButtonItem10.Name = "barButtonItem10";
            //
            // barButtonItem11
            //
            this.barButtonItem11.Caption = "Buy";
            this.barButtonItem11.Id = 771;
            this.barButtonItem11.Name = "barButtonItem11";
            //
            // barButtonItem12
            //
            this.barButtonItem12.Caption = "Subscription Info";
            this.barButtonItem12.Id = 116;
            this.barButtonItem12.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem12.ImageOptions.Image")));
            this.barButtonItem12.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem12.ImageOptions.LargeImage")));
            this.barButtonItem12.Name = "barButtonItem12";
            toolTipItem28.Text = "Signup, view or change your subscription plan.";
            superToolTip30.Items.Add(toolTipItem28);
            this.barButtonItem12.SuperTip = superToolTip30;
            //
            // barButtonItem13
            //
            this.barButtonItem13.Caption = "Terms and Conditions";
            this.barButtonItem13.Id = 223;
            this.barButtonItem13.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem13.ImageOptions.Image")));
            this.barButtonItem13.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem13.ImageOptions.LargeImage")));
            this.barButtonItem13.Name = "barButtonItem13";
            //
            // barStaticItem10
            //
            this.barStaticItem10.Caption = "Build: v.********";
            this.barStaticItem10.Id = 2311;
            this.barStaticItem10.Name = "barStaticItem10";
            //
            // barStaticItem11
            //
            this.barStaticItem11.Caption = "0";
            this.barStaticItem11.Id = 374;
            this.barStaticItem11.Name = "barStaticItem11";
            //
            // barDockingMenuItem2
            //
            this.barDockingMenuItem2.Caption = "Docking";
            this.barDockingMenuItem2.Id = 5515;
            this.barDockingMenuItem2.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barDockingMenuItem2.ImageOptions.Image")));
            this.barDockingMenuItem2.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barDockingMenuItem2.ImageOptions.LargeImage")));
            this.barDockingMenuItem2.Name = "barDockingMenuItem2";
            //
            // barHeaderItem5
            //
            this.barHeaderItem5.Caption = "                     ";
            this.barHeaderItem5.Id = 871;
            this.barHeaderItem5.Name = "barHeaderItem5";
            //
            // barHeaderItem6
            //
            this.barHeaderItem6.Caption = "                                                             ";
            this.barHeaderItem6.Id = 381;
            this.barHeaderItem6.Name = "barHeaderItem6";
            //
            // barStaticItem12
            //
            this.barStaticItem12.Id = 117;
            this.barStaticItem12.Name = "barStaticItem12";
            //
            // barButtonItem14
            //
            this.barButtonItem14.Caption = "Reset Layout";
            this.barButtonItem14.Id = 118;
            this.barButtonItem14.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem14.ImageOptions.Image")));
            this.barButtonItem14.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem14.ImageOptions.LargeImage")));
            this.barButtonItem14.Name = "barButtonItem14";
            toolTipItem29.Text = "Application panels and grid columns will be set to a default state.";
            superToolTip31.Items.Add(toolTipItem29);
            this.barButtonItem14.SuperTip = superToolTip31;
            //
            // ribbonPage6
            //
            this.ribbonPage6.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup11});
            this.ribbonPage6.Name = "ribbonPage6";
            this.ribbonPage6.Text = "Home";
            //
            // ribbonPageGroup11
            //
            this.ribbonPageGroup11.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroup11.ItemLinks.Add(this.barButtonItem10);
            this.ribbonPageGroup11.ItemLinks.Add(this.barButtonItem8);
            this.ribbonPageGroup11.ItemLinks.Add(this.barButtonItem11);
            this.ribbonPageGroup11.Name = "ribbonPageGroup11";
            //
            // ribbonPage7
            //
            this.ribbonPage7.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup12,
            this.ribbonPageGroup13});
            this.ribbonPage7.Name = "ribbonPage7";
            this.ribbonPage7.Text = "Options";
            //
            // ribbonPageGroup12
            //
            this.ribbonPageGroup12.Name = "ribbonPageGroup12";
            this.ribbonPageGroup12.Text = "Skin";
            //
            // ribbonPageGroup13
            //
            this.ribbonPageGroup13.ItemLinks.Add(this.barDockingMenuItem2);
            this.ribbonPageGroup13.ItemLinks.Add(this.barButtonItem14);
            this.ribbonPageGroup13.Name = "ribbonPageGroup13";
            this.ribbonPageGroup13.Text = "Layout";
            //
            // ribbonPage8
            //
            this.ribbonPage8.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup14,
            this.ribbonPageGroup15});
            this.ribbonPage8.Name = "ribbonPage8";
            this.ribbonPage8.Text = "Help";
            //
            // ribbonPageGroup14
            //
            this.ribbonPageGroup14.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroup14.ItemLinks.Add(this.barButtonItem12);
            this.ribbonPageGroup14.ItemLinks.Add(this.barButtonItem9);
            this.ribbonPageGroup14.ItemLinks.Add(this.barButtonItem13);
            this.ribbonPageGroup14.Name = "ribbonPageGroup14";
            //
            // ribbonPageGroup15
            //
            this.ribbonPageGroup15.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroup15.ItemLinks.Add(this.barStaticItem10);
            this.ribbonPageGroup15.Name = "ribbonPageGroup15";
            //
            // ribbonStatusBar4
            //
            this.ribbonStatusBar4.Location = new System.Drawing.Point(0, 146);
            this.ribbonStatusBar4.Name = "ribbonStatusBar4";
            this.ribbonStatusBar4.Ribbon = this.ribbonControl3;
            this.ribbonStatusBar4.Size = new System.Drawing.Size(979, 27);
            //
            // documentManager1
            //
            this.documentManager1.BarAndDockingController = this.barAndDockingController1;
            this.documentManager1.ContainerControl = this;
            this.documentManager1.MenuManager = this.ribbonControl1;
            this.documentManager1.View = this.tabbedView1;
            this.documentManager1.ViewCollection.AddRange(new DevExpress.XtraBars.Docking2010.Views.BaseView[] {
            this.tabbedView1});
            //
            // tabbedView1
            //
            this.tabbedView1.AllowResetLayout = DevExpress.Utils.DefaultBoolean.True;
            this.tabbedView1.DocumentGroupProperties.HeaderLocation = DevExpress.XtraTab.TabHeaderLocation.Right;
            this.tabbedView1.DocumentGroupProperties.HeaderOrientation = DevExpress.XtraTab.TabOrientation.Vertical;
            this.tabbedView1.DocumentGroupProperties.ShowDocumentSelectorButton = false;
            this.tabbedView1.DocumentGroups.AddRange(new DevExpress.XtraBars.Docking2010.Views.Tabbed.DocumentGroup[] {
            this.documentGroup1,
            this.documentGroup2});
            this.tabbedView1.Documents.AddRange(new DevExpress.XtraBars.Docking2010.Views.BaseDocument[] {
            this.document5,
            this.document4,
            this.document2,
            this.document7,
            this.document1,
            this.document6,
            this.document3,
            this.document8,
            this.document9});
            this.tabbedView1.Orientation = System.Windows.Forms.Orientation.Vertical;
            dockingContainer1.Element = this.documentGroup1;
            dockingContainer1.Length.UnitValue = 1.3740405853287971D;
            dockingContainer2.Element = this.documentGroup2;
            dockingContainer2.Length.UnitValue = 1.120923020288437D;
            this.tabbedView1.RootContainer.Nodes.AddRange(new DevExpress.XtraBars.Docking2010.Views.Tabbed.DockingContainer[] {
            dockingContainer1,
            dockingContainer2});
            this.tabbedView1.RootContainer.Orientation = System.Windows.Forms.Orientation.Vertical;
            //
            // ribbonPageGroup18
            //
            this.ribbonPageGroup18.Name = "ribbonPageGroup18";
            this.ribbonPageGroup18.Text = "ribbonPageGroup17";
            //
            // ribbonPageGroup19
            //
            this.ribbonPageGroup19.Name = "ribbonPageGroup19";
            this.ribbonPageGroup19.Text = "ribbonPageGroup17";
            //
            // popupMenuItemDetails
            //
            this.popupMenuItemDetails.ItemLinks.Add(this.barButtonItemCustomizeLayout);
            this.popupMenuItemDetails.ItemLinks.Add(this.barButtonItemResetLayout);
            this.popupMenuItemDetails.ItemLinks.Add(this.barButtonItemIncreaseFont);
            this.popupMenuItemDetails.ItemLinks.Add(this.barButtonItemDecreaseFont);
            this.popupMenuItemDetails.ItemLinks.Add(this.barButtonItemMenuHelp);
            this.popupMenuItemDetails.Name = "popupMenuItemDetails";
            this.popupMenuItemDetails.Ribbon = this.ribbonControl1;
            //
            // comboBoxEdit1
            //
            this.comboBoxEdit1.Location = new System.Drawing.Point(126, 129);
            this.comboBoxEdit1.MenuManager = this.ribbonControl1;
            this.comboBoxEdit1.Name = "comboBoxEdit1";
            this.comboBoxEdit1.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxEdit1.Size = new System.Drawing.Size(100, 20);
            this.comboBoxEdit1.TabIndex = 0;
            //
            // alertControl1
            //
            this.alertControl1.AllowHtmlText = true;
            this.alertControl1.AppearanceCaption.Options.UseTextOptions = true;
            this.alertControl1.AppearanceCaption.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.alertControl1.AppearanceText.Options.UseTextOptions = true;
            this.alertControl1.AppearanceText.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.alertControl1.AutoHeight = true;
            alertButton1.Hint = "View";
            alertButton1.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Eye;
            alertButton1.ImageOptions.SvgImageSize = new System.Drawing.Size(15, 15);
            alertButton1.Name = "alertButtoneBay";
            alertButton2.Hint = "Buy";
            alertButton2.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Buy1;
            alertButton2.ImageOptions.SvgImageSize = new System.Drawing.Size(15, 15);
            alertButton2.Name = "alertButtonBuy";
            alertButton3.Hint = "Checkout page";
            alertButton3.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Checkout;
            alertButton3.ImageOptions.SvgImageSize = new System.Drawing.Size(15, 15);
            alertButton3.Name = "alertCheckoutPage";
            alertButton4.Hint = "Make an offer";
            alertButton4.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.MakeOfferDark;
            alertButton4.ImageOptions.SvgImageSize = new System.Drawing.Size(15, 15);
            alertButton4.Name = "alertButtonOffer";
            this.alertControl1.Buttons.Add(alertButton1);
            this.alertControl1.Buttons.Add(alertButton2);
            this.alertControl1.Buttons.Add(alertButton3);
            this.alertControl1.Buttons.Add(alertButton4);
            this.alertControl1.FormDisplaySpeed = DevExpress.XtraBars.Alerter.AlertFormDisplaySpeed.Fast;
            this.alertControl1.FormMaxCount = 10;
            this.alertControl1.FormShowingEffect = DevExpress.XtraBars.Alerter.AlertFormShowingEffect.SlideHorizontal;
            this.alertControl1.PopupMenu = this.popupMenuTrayAlert;
            this.alertControl1.AlertClick += new DevExpress.XtraBars.Alerter.AlertClickEventHandler(this.AlertControl1_AlertClick);
            this.alertControl1.ButtonClick += new DevExpress.XtraBars.Alerter.AlertButtonClickEventHandler(this.alertControl1_ButtonClick);
            this.alertControl1.BeforeFormShow += new DevExpress.XtraBars.Alerter.AlertFormEventHandler(this.alertControl1_BeforeFormShow);
            this.alertControl1.FormLoad += new DevExpress.XtraBars.Alerter.AlertFormLoadEventHandler(this.AlertControl1_FormLoad);
            //
            // popupMenuTrayAlert
            //
            this.popupMenuTrayAlert.ItemLinks.Add(this.barCheckItemSoundAlert);
            this.popupMenuTrayAlert.ItemLinks.Add(this.barButtonItemTrayAlertOptions);
            this.popupMenuTrayAlert.ItemLinks.Add(this.barButtonItemCloseAll);
            this.popupMenuTrayAlert.Name = "popupMenuTrayAlert";
            this.popupMenuTrayAlert.Ribbon = this.ribbonControl1;
            //
            // ribbonPageGroup1
            //
            this.ribbonPageGroup1.AllowTextClipping = false;
            this.ribbonPageGroup1.CaptionButtonVisible = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonPageGroup1.ItemLinks.Add(this.barButtonItemShortcuts);
            this.ribbonPageGroup1.Name = "ribbonPageGroup1";
            this.ribbonPageGroup1.Text = "Shortcuts";
            //
            // ribbonPage9
            //
            this.ribbonPage9.Name = "ribbonPage9";
            this.ribbonPage9.Text = "ribbonPage9";
            //
            // ribbonPage10
            //
            this.ribbonPage10.Name = "ribbonPage10";
            this.ribbonPage10.Text = "ribbonPage10";
            //
            // Form1
            //
            this.Appearance.BackColor = System.Drawing.Color.White;
            this.Appearance.ForeColor = System.Drawing.Color.White;
            this.Appearance.Options.UseBackColor = true;
            this.Appearance.Options.UseForeColor = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1078, 581);
            this.Controls.Add(this.popupContainerControlCategory);
            this.Controls.Add(this.ribbonStatusBar1);
            this.Controls.Add(this.ribbonControl1);
            this.IconOptions.Icon = ((System.Drawing.Icon)(resources.GetObject("Form1.IconOptions.Icon")));
            this.IsMdiContainer = true;
            this.Margin = new System.Windows.Forms.Padding(2);
            this.Name = "Form1";
            this.Ribbon = this.ribbonControl1;
            this.StatusBar = this.ribbonStatusBar1;
            this.Text = "uBuyFirst";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.ClosingMainForm);
            this.Load += new System.EventHandler(this.Form1_Load);
            this.Shown += new System.EventHandler(this.Form1_Shown);
            this.MouseMove += new System.Windows.Forms.MouseEventHandler(this.MouseMoveResetIdle);
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemButtonNewKeyword)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.documentGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.document4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.document6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.document1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.document5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.document2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.document3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.document8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.documentGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.document7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.document9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemImageEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemButtonEditVisible)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemButtonEditInvisible)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEditBrowser)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBoxSite)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEditIdleTimeout)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenuDockPanels)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBoxTimeZone)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenuOpenInBrowser)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemColorEditDescription)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEditDescriptionZoom)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBoxProfile)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEditInitialSearch)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemResultsMaxCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEditDailySpendLimit)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoExEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemColorEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoExEdit2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemColorEdit2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemOfficeColorEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemColorPickEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemOfficeColorPickEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemRichEditFontSizeEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemFontEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemFontStyle1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemBorderLineStyle1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lstchkXfilterList)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.zoomTrackBarPictures.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.zoomTrackBarPictures)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.galleryControl1)).EndInit();
            this.galleryControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.picBoxEbayLogo)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.dockFilters.ResumeLayout(false);
            this.controlContainer1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).EndInit();
            this.panelControl2.ResumeLayout(false);
            this.dockSearchQueries.ResumeLayout(false);
            this.controlContainer2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2.Panel1)).EndInit();
            this.splitContainerControl2.Panel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2.Panel2)).EndInit();
            this.splitContainerControl2.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeList1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEditEnabled)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEditFolder)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEditFolderOpen)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemDescription)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemPopupContainerEditCategory)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupContainerControlCategory)).EndInit();
            this.popupContainerControlCategory.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeListCategory)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckedComboBoxEditCondition)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBoxLocatedIn)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBoxShipsTo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemComboBoxSellerType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryIntervalEdit)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemPopupContainerEditFilter)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupContainerControl1)).EndInit();
            this.popupContainerControl1.ResumeLayout(false);
            this.popupContainerControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemViews)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckedComboBoxEditListingType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEditThreads)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemEmpty4Filter)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemEditTextData)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTreeListLookUpEditCategory)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTreeListLookUpEdit1TreeList)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            this.panelControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkUseAPI.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditShowSoldItems.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkUseRSS.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkUseRSS2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditWhitespaceTokenizer.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditFindReqMaxThreads.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditGetItemDetailsMaxThreads.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkUpdateItemStatusFor2Min.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkDownloadAvatars.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkDownloadOtherImages.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkDownloadDescription.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControlDebugInfo)).EndInit();
            this.panelControlDebugInfo.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.panelControlKeywordsButtons)).EndInit();
            this.panelControlKeywordsButtons.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.categoriesBindingSource)).EndInit();
            this.dockItemProperties.ResumeLayout(false);
            this.controlContainer5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).EndInit();
            this.layoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.lcEbayAccount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciVariation)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciUPC)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciTerm)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciSoldTime)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShipAdditionalItem)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShippingType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciPageViews)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciPostedTime)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciFoundTime)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciEbayWebsite)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciEbayAccount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciCategoryName)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciCategoryID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciBestOffer)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciAutoPay)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciItemPrice)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShipping)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciToCountry)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciFromCountry)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciPayment)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShippingDays)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciTotalPrice)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciConditionDescription)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciCondition)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciReturns)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciTitle)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.simpleSeparator1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciSellerName)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciLocation)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciItemID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciFeedbackScore)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciFeedbackRating)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciQuantity)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.simpleSeparator2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.simpleSeparator3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciAuctionPrice)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciListingType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciBids)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lcipanelPicturesControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelPicturesControl)).EndInit();
            this.panelPicturesControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlForPicture)).EndInit();
            this.layoutControlForPicture.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.panelPicturesSettingControl)).EndInit();
            this.panelPicturesSettingControl.ResumeLayout(false);
            this.panelPicturesSettingControl.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.zoomTrackBarExpanded.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.zoomTrackBarExpanded)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.svgLargeImageBox)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.svgSmallImageBox)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroupPictures)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lcipanelPicturesOpenSettingsControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lcipanelPicturesSettingControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dockManager1)).EndInit();
            this.dockDescription.ResumeLayout(false);
            this.dockPanel5_Container.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControlBrowser.Panel1)).EndInit();
            this.splitContainerControlBrowser.Panel1.ResumeLayout(false);
            this.splitContainerControlBrowser.Panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControlBrowser.Panel2)).EndInit();
            this.splitContainerControlBrowser.Panel2.ResumeLayout(false);
            this.splitContainerControlBrowser.Panel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControlBrowser)).EndInit();
            this.splitContainerControlBrowser.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.zoomTrackBarBrowser.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.zoomTrackBarBrowser)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorPickBrowser.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.flyoutPanelBrowser)).EndInit();
            this.flyoutPanelBrowser.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.flyoutPanelControl1)).EndInit();
            this.dockLog.ResumeLayout(false);
            this.dockPanel7_Container.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabLog)).EndInit();
            this.xtraTabLog.ResumeLayout(false);
            this.xtraTabPageFilterLog.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.panelControlFilterLog)).EndInit();
            this.panelControlFilterLog.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.memoEditFilterLog.Properties)).EndInit();
            this.xtraTabPageErrorLog.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.memoEditErrorLog.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControlErrorLog)).EndInit();
            this.panelControlErrorLog.ResumeLayout(false);
            this.panelControlErrorLog.PerformLayout();
            this.dockPictures.ResumeLayout(false);
            this.controlContainer6.ResumeLayout(false);
            this.dockPanelBuy.ResumeLayout(false);
            this.controlContainer4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerBuyOffer.Panel1)).EndInit();
            this.splitContainerBuyOffer.Panel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerBuyOffer.Panel2)).EndInit();
            this.splitContainerBuyOffer.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerBuyOffer)).EndInit();
            this.splitContainerBuyOffer.ResumeLayout(false);
            this.dockPanelExternalData.ResumeLayout(false);
            this.controlContainer3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlCefBrowser)).EndInit();
            this.layoutControlCefBrowser.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.panelCefBrowser)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroupExternalData)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).EndInit();
            this.dockPanelWatchlist.ResumeLayout(false);
            this.controlContainer7.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.layoutWatchlistControls)).EndInit();
            this.layoutWatchlistControls.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chkRefreshAndNotifyWatchlist.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeSpanWatchlistRefreshInterval.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutWatchlistRoot)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciWatchlist)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciWatchlistRefreshInterval)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitterItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitterItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem5)).EndInit();
            this.dockMainGrid.ResumeLayout(false);
            this.dockPanel6_Container.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.documentManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabbedView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenuItemDetails)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenuTrayAlert)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.behaviorManager1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }



        #endregion
        private ToolTip toolTip1;
        internal ImageList imageList16;
        public WebBrowser webBrowser1;
        private FontDialog fontDialog1;
        private OpenFileDialog openFileDialog1;
        private BackgroundWorker bgCheckoutWorker;
        private ColorDialog colorDialog1;
        private OpenFileDialog openFileDialog2;
        public NotifyIcon notifyIcon1;
        private ContextMenuStrip contextMenuStrip1;
        public ToolStripMenuItem ToolStripMenuItemShow;
        private ToolStripMenuItem toolStripMenuItemExit;
        private PictureBox picBoxEbayLogo;
        private FontDialog fontDialogDGV1;
        private GridControl gridControl1;
        private AdvBandedGridView gridView1;
        private SimpleButton btnRemoveXfilter;
        private SimpleButton btnAddXfilter;
        private CheckedListBoxControl lstchkXfilterList;
        private SimpleButton btnEditXfilter;

        private ControlContainer dockPanel2_Container;
        private ControlContainer dockPanel3_Container;
        private ControlContainer dockPanel1_Container;
        private SimpleButton btnCopyFilter;
        internal GalleryControl galleryControl1;
        private GalleryControlClient galleryControlClient1;
        private ZoomTrackBarControl zoomTrackBarPictures;
        private FormAssistant formAssistant1;
        private RibbonControl ribbonControl1;
        private RibbonPage ribbonPage1;
        private RibbonPageGroup ribbonPageGroupHome;
        private RibbonStatusBar ribbonStatusBar1;
        private BarButtonItem barButtonClear;
        private RibbonPage ribbonPageHelp;
        private WorkspaceManager workspaceManager1;
        private DockPanel dockMainGrid;
        private ControlContainer dockPanel6_Container;
        private DockPanel dockPictures;
        private ControlContainer controlContainer6;
        private PanelControl panelPicturesSettingControl;
        private ZoomTrackBarControl zoomTrackBarExpanded;
        private PanelControl panelPicturesControl;
        private SimpleButton pictureSettingsButton2;
        private SimpleButton btnEditPictureProperties;
        private SimpleButton pictureSettingsButton1;
        private SvgImageBox svgSmallImageBox;
        private SvgImageBox svgLargeImageBox;
        internal DockPanel dockDescription;
        private ControlContainer dockPanel5_Container;
        private DockPanel dockFilters;
        private ControlContainer controlContainer1;
        private DockManager dockManager1;
        private BarButtonItem barButtonSupport;
        internal BarStaticItem barStaticLicense;
        public BarStaticItem barStaticErrorsVal;
        private BarButtonItem barButtonStart;
        internal BarButtonItem barButtonBuy;
        private BarButtonItem barButtonSubscriptionInfo;
        private BarButtonItem barButtonTermsConditions;
        private RibbonPageGroup ribbonPageGroupSupport;
        private BarStaticItem barStaticBuildVersion;
        private DockPanel dockLog;
        private ControlContainer dockPanel7_Container;
        public MemoEdit memoEditErrorLog;
        private HyperlinkLabelControl hyperlinkLabelOpoenLogFolder;
        private SimpleButton btnClearLogs;
        private ZoomTrackBarControl zoomTrackBarBrowser;
        private ColorPickEdit colorPickBrowser;
        private RibbonPageGroup ribbonPageGroup4;
        private RibbonPage ribbonPageView;
        private RibbonPageGroup ribbonPageGroupSkin;
        private RibbonPageGroup ribbonPageGroupLayout;
        private DocumentManager documentManager1;
        private TabbedView tabbedView1;

        private BarDockingMenuItem menuDocking;
        private BarStaticItem barStaticProgress;

        private BarWorkspaceMenuItem barWorkspaceMenuItem;
        private RepositoryItemImageEdit repositoryItemImageEdit1;
        private RibbonStatusBar ribbonStatusBar2;
        private DockPanel dockPanelBuy;
        private ControlContainer controlContainer4;
        internal SimpleButton panelBuyButton;
        internal BarButtonItem barButtonResetWorkspace;
        private GridBand gridBand1;
        private GridBand gridBand2;
        private GridBand gridBand3;
        private ToolTipController toolTipController2;
        private RepositoryItemButtonEdit repositoryItemButtonEditVisible;
        private RepositoryItemButtonEdit repositoryItemButtonEditInvisible;
        private DockPanel dockItemProperties;
        private ControlContainer controlContainer5;
        internal LayoutControl layoutControl1;
        private LayoutControlGroup layoutControlGroup1;
        private RibbonControl ribbonControl2;
        private BarButtonItem barButtonItem1;
        private BarButtonItem barButtonItem2;
        private BarStaticItem barStaticItem1;
        private BarStaticItem barStaticItem3;
        private BarButtonItem barButtonItem3;
        private BarButtonItem barButtonItem4;
        private BarButtonItem barButtonItem5;
        private BarButtonItem barButtonItem6;
        private BarStaticItem barStaticItem4;
        private BarStaticItem barStaticItem5;
        private BarDockingMenuItem barDockingMenuItem1;
        private BarHeaderItem barHeaderItem3;
        private BarHeaderItem barHeaderItem4;
        private BarStaticItem barStaticItem6;
        private BarButtonItem barButtonItem7;
        private RibbonPage ribbonPage2;
        private RibbonPageGroup ribbonPageGroup6;
        private RibbonPage ribbonPage3;
        private RibbonPageGroup ribbonPageGroup7;
        private RibbonPageGroup ribbonPageGroup8;
        private RibbonPage ribbonPage5;
        private RibbonPageGroup ribbonPageGroup9;
        private RibbonPageGroup ribbonPageGroup10;
        private RibbonStatusBar ribbonStatusBar3;
        private RibbonControl ribbonControl3;
        private BarButtonItem barButtonItem8;
        private BarButtonItem barButtonItem9;
        private BarStaticItem barStaticItem7;
        private BarButtonItem barButtonItem10;
        private BarButtonItem barButtonItem11;
        private BarButtonItem barButtonItem12;
        private BarButtonItem barButtonItem13;
        private BarStaticItem barStaticItem10;
        private BarStaticItem barStaticItem11;
        private BarDockingMenuItem barDockingMenuItem2;
        private BarHeaderItem barHeaderItem5;
        private BarHeaderItem barHeaderItem6;
        private BarStaticItem barStaticItem12;
        private BarButtonItem barButtonItem14;
        private RibbonPage ribbonPage6;
        private RibbonPageGroup ribbonPageGroup11;
        private RibbonPage ribbonPage7;
        private RibbonPageGroup ribbonPageGroup12;
        private RibbonPageGroup ribbonPageGroup13;
        private RibbonPage ribbonPage8;
        private RibbonPageGroup ribbonPageGroup14;
        private RibbonPageGroup ribbonPageGroup15;
        private RibbonStatusBar ribbonStatusBar4;
        internal LabelControl lcItemID;
        internal LabelControl lcTerm;
        private LayoutControlItem lciTerm;
        internal LabelControl lcTitle;
        internal LabelControl lcTotalPrice;
        internal LabelControl lcCondition;
        internal LabelControl lcReturns;
        internal LabelControl lcBestOffer;
        internal LabelControl lcFoundTime;
        internal LabelControl lcLocation;
        internal LabelControl lcFromCountry;
        internal LabelControl lcToCountry;
        internal LabelControl lcAutoPay;
        internal LabelControl lcCategoryID;
        internal LabelControl lcCategoryName;
        internal LabelControl lcConditionDescription;
        internal LabelControl lcFeedbackRating;
        private LayoutControlItem lciBestOffer;
        private LayoutControlItem lciFoundTime;
        private LayoutControlItem lciFromCountry;
        private LayoutControlItem lciToCountry;
        private LayoutControlItem lciAutoPay;
        private LayoutControlItem lciCategoryID;
        private LayoutControlItem lciCategoryName;
        internal LabelControl lcFeedbackScore;
        internal LabelControl lcItemPrice;
        internal LabelControl lcPostedTime;
        internal LabelControl lcQuantity;
        internal LabelControl lcSellerName;
        internal LabelControl lcShipping;
        internal LabelControl lcShippingType;
        internal LabelControl lcShipAdditionalItem;
        internal LabelControl lcSoldTime;
        internal LabelControl lcEbayWebsite;
        internal LabelControl lcPageViews;
        internal LabelControl lcUPC;
        internal LabelControl lcVariation;
        private LayoutControlItem lciItemPrice;
        private LayoutControlItem lciPostedTime;
        private LayoutControlItem lciShipping;
        private LayoutControlItem lciShippingType;
        private LayoutControlItem lciShipAdditionalItem;
        private LayoutControlItem lciSoldTime;
        private LayoutControlItem lciEbayWebsite;
        private LayoutControlItem lciPageViews;
        private LayoutControlItem lciUPC;
        private LayoutControlItem lciVariation;
        internal ComboBoxEdit lcEbayAccount;
        private LayoutControlItem lciEbayAccount;
        private SimpleSeparator simpleSeparator1;
        private BarButtonItem barButtonCustomColumns;
        private RibbonPageGroup ribbonPageGroupItemDetails;
        private RibbonPage ribbonPageDescriptionSettings;
        private RepositoryItemMemoExEdit repositoryItemMemoExEdit1;
        private RepositoryItemColorEdit repositoryItemColorEdit1;
        private RepositoryItemMemoEdit repositoryItemMemoEdit1;
        private RepositoryItemMemoExEdit repositoryItemMemoExEdit2;
        private RepositoryItemColorEdit repositoryItemColorEdit2;
        private RepositoryItemOfficeColorEdit repositoryItemOfficeColorEdit1;
        private RepositoryItemColorPickEdit repositoryItemColorPickEdit1;
        private RepositoryItemOfficeColorPickEdit repositoryItemOfficeColorPickEdit1;
        private RibbonPageGroup ribbonPageGroup18;
        private RibbonPageGroup ribbonPageGroup19;
        private RibbonPageGroup ribbonPageGroupHighLightWords;
        private RepositoryItemMemoEdit repositoryItemMemoEdit2;
        private RibbonPage ribbonPageGrid;
        private RibbonPageGroup ribbonPageGroupAppearance;
        private BarButtonItem barButtonItemGridFont;
        private BarEditItem barEditItemRowHeight;
        private RepositoryItemSpinEdit repositoryItemSpinEditIdleTimeout;
        private RepositoryItemRichEditFontSizeEdit repositoryItemRichEditFontSizeEdit1;
        private RepositoryItemFontEdit repositoryItemFontEdit1;
        private RepositoryItemFontStyle repositoryItemFontStyle1;
        private BarEditItem barEditItemAutoSelect;
        private RepositoryItemSpinEdit repositoryItemSpinEdit1;
        private RibbonPageGroup ribbonPageGroupTopRow;
        private RibbonPageGroup ribbonPageGroupEBayAccounts;
        private BarButtonItem barButtonEbayAccounts;
        private RibbonPage ribbonPageApplicationOptions;
        private BarButtonItem barButtonItemHightlightWords;
        public BarCheckItem barCheckItemMaximizewindow;
        private BarCheckItem barCheckItemSoundAlert;
        private RepositoryItemBorderLineStyle repositoryItemBorderLineStyle1;
        private BarButtonItem barButtonItemSelectSound;
        private RibbonPageGroup ribbonPageGroupNewItem;
        private BarCheckItem barEnableIdleTimeout;
        private BarEditItem barIdleTimeoutMinutes;
        private BarButtonItem barButtonItemTimeSync;
        private BarStaticItem barStaticItemTimeDiffText;
        private RibbonPageGroup ribbonPageGroupTimeSync;
        private RibbonPageGroup ribbonPageGroupIdleTimeout;
        private DefaultLookAndFeel defaultLookAndFeel1;

        private PopupMenu popupMenuItemDetails;
        private BarButtonItem barButtonItemDockPanels;
        private PopupMenu popupMenuDockPanels;
        private BarButtonItem barButtonItemCustomizeLayout;
        private BarButtonItem barButtonItemResetLayout;
        private BarButtonItem barButtonItemIncreaseFont;
        private BarButtonItem barButtonItemDecreaseFont;
        private BarCheckItem barCheckItemNone;
        private ComboBoxEdit comboBoxEdit1;
        private DocumentGroup documentGroup1;
        private SimpleButton btnEditItemProperties;
        private BarButtonItem barButtonItemKeywords;
        private BarButtonItem barButtonItemFilters;
        private RibbonPageGroup ribbonPageGroupKeywordsFilters;
        internal BarButtonItem barButtonMakeOffer;
        private BarAndDockingController barAndDockingController1;
        private SplitContainerControl splitContainerBuyOffer;
        internal SimpleButton btnMakeOffer;
        private LayoutConverter layoutConverter1;
        private BarCheckItem barCheckItemAutoStartSearch;
        private RibbonPageGroup ribbonPageGroupProgramLaunch;
        private BarEditItem barEditItemTimeZone;
        private RepositoryItemComboBox repositoryItemComboBoxTimeZone;
        private BarCheckItem barCheckItemStartOnBoot;
        private BarCheckItem barCheckItemPushBullet;
        private DockPanel dockSearchQueries;
        private ControlContainer controlContainer2;
        internal DevExpress.XtraTreeList.TreeList treeList1;
        private PopupContainerControl popupContainerControl1;
        private LabelControl lblTermName;
        private FilterEditorControl filterControlTerm;
        private RepositoryItemPopupContainerEdit repositoryItemPopupContainerEditFilter;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cAlias;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cKeywords;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cSearchInDescription;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cPriceMin;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cPriceMax;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cCategoryID;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cSite;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cLocatedIn;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cShipsTo;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cZip;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cSellers;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cSellerType;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cInterval;
        private SimpleButton btnSavePopupFilter;
        private SimpleButton btnRemoveSearch;
        private SimpleButton btnNewChildTerm;
        private SimpleButton btnNewSearchQuery;
        private RepositoryItemComboBox repositoryItemComboBoxSellerType;
        private RepositoryItemTimeSpanEdit repositoryIntervalEdit;
        private RepositoryItemSpinEdit repositoryItemSpinEditThreads;
        private RepositoryItemCheckEdit repositoryItemDescription;
        private RepositoryItemComboBox repositoryItemComboBoxSite;
        private RepositoryItemComboBox repositoryItemComboBoxLocatedIn;
        private RepositoryItemComboBox repositoryItemComboBoxShipsTo;
        private RepositoryItemButtonEdit repositoryItemEmpty4Filter;
        private RepositoryItemButtonEdit repositoryItemButtonNewKeyword;
        private RepositoryItemTextEdit repositoryItemEditTextData;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cCondition;
        private RepositoryItemCheckedComboBoxEdit repositoryItemCheckedComboBoxEditCondition;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cFilter;
        private SimpleButton btnExportSearches;
        private SimpleButton btnImportSearches;
        private DevExpress.XtraBars.Alerter.AlertControl alertControl1;
        private BarButtonItem barButtonItemTrayAlert;
        private BarButtonItem barButtonItemCloseAll;
        private PopupMenu popupMenuTrayAlert;
        private BarButtonItem barButtonItemTrayAlertOptions;
        private SimpleButton btnClearFilter;
        private BarEditItem barEditItemHwid;
        private RepositoryItemTextEdit repositoryItemTextEdit1;
        private PanelControl panelControlDebugInfo;
        private RichTextBox txtDebugInfo1;
        private LabelControl lblFindReqMaxThreads;
        private SpinEdit spinEditFindReqMaxThreads;
        private CheckEdit chkUpdateItemStatusFor2Min;
        private CheckEdit chkDownloadDescription;
        private CheckEdit chkDownloadOtherImages;
        private CheckEdit chkDownloadAvatars;
        private LabelControl lblGetItemDetailsMaxThreads;
        private SpinEdit spinEditGetItemDetailsMaxThreads;
        private SimpleButton btnDebugStatsInfo;
        private SplitContainerControl splitContainerControl2;
        private BarButtonItem barButtonItemPushBullet;
        internal LabelControl lcPayment;
        private LayoutControlItem lciPayment;
        private CheckEdit checkUseAPI;
        private CheckEdit checkUseRSS2;
        private CheckEdit checkUseRSS;
        private CheckEdit checkEditShowSoldItems;
        private BarStaticItem barStaticNotification;
        private Document document4;
        private DocumentGroup documentGroup2;
        private Document document5;
        private SimpleSeparator simpleSeparator2;
        private SimpleSeparator simpleSeparator3;
        private EmptySpaceItem emptySpaceItem1;
        private EmptySpaceItem emptySpaceItem2;
        private Document document6;
        private BarButtonItem barButtonItemMenuHelp;
        private BarButtonItem barButtonItemHelp;
        private BarButtonItem barButtonRestartOnUpdate;
        private HyperlinkLabelControl linkeBayPrivacyPolicy;
        internal HyperlinkLabelControl linkReportItem;
        private HyperlinkLabelControl linkeBayUserAgreement;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cResultWindow;
        private RepositoryItemMRUEdit repositoryItemViews;
        private BarButtonItem barButtonItemViews;
        private RibbonPageGroup ribbonPageGroupResults;
        private RepositoryItemTextEdit repositoryItemTextEditBrowser;
        private Document document1;
        private DevExpress.Utils.Behaviors.BehaviorManager behaviorManager1;
        //private FilterAdapter filterAdapter2;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cListingType;
        private RepositoryItemCheckedComboBoxEdit repositoryItemCheckedComboBoxEditListingType;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cJobId;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cRequiredQuantity;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cPurchasedQuantity;
        private CheckEdit checkEditWhitespaceTokenizer;
        private FilterAdapter filterAdapter2;
        internal LabelControl lcBids;
        internal LabelControl lcListingType;
        internal LabelControl lcAuctionPrice;
        private Document document2;
        private Document document7;
        private SimpleButton btnBrowserSettings;
        private FlyoutPanel flyoutPanelBrowser;
        private FlyoutPanelControl flyoutPanelControl1;
        private SimpleButton btnHighlightWords;
        private RepositoryItemTreeListLookUpEdit repositoryItemTreeListLookUpEditCategory;
        private DevExpress.XtraTreeList.TreeList repositoryItemTreeListLookUpEdit1TreeList;
        private BindingSource categoriesBindingSource;
        private PopupContainerControl popupContainerControlCategory;
        private DevExpress.XtraTreeList.TreeList treeListCategory;
        private RepositoryItemPopupContainerEdit repositoryItemPopupContainerEditCategory;
        private BarCheckItem barCheckImmediatePayment;
        private RibbonPageGroup ribbonPageGroupShortcuts;
        private BarButtonItem barButtonItemShortcuts;
        private SkinDropDownButtonItem skinDropDownButtonItem1;
        private BarButtonItem barButtonItemOpenInBrowser;
        private PopupMenu popupMenuOpenInBrowser;
        private SimpleButton btnExportFilters;
        private SimpleButton btnImportFilters;
        private SimpleButton btnSortFilters;
        private BarCheckItem barCheckNoConfirmations;
        private LayoutControlItem lciShippingDays;
        internal LabelControl lcShippingDays;
        private BarButtonItem barButtonItemTelegram;
        private PanelControl panelControlKeywordsButtons;
        private PanelControl panelControl1;
        private PanelControl panelControlErrorLog;
        private PanelControl panelControl2;
        private SplitContainerControl splitContainerControlBrowser;
        private BarEditItem barEditItemDescriptionColor;
        private RepositoryItemColorEdit repositoryItemColorEditDescription;
        private RibbonPageGroup ribbonPageGroupColor;
        private RibbonPageGroup ribbonPageGroupDescriptionZoom;
        private BarEditItem barEditItemDescriptionZoom;
        private RepositoryItemSpinEdit repositoryItemSpinEditDescriptionZoom;
        private RibbonPage ribbonPageCheckout;
        private RibbonPageGroup ribbonPageGroupCheckout;
        private BarEditItem barEditItemProfile;
        private RepositoryItemComboBox repositoryItemComboBoxProfile;
        private RibbonPageGroup ribbonPageGroupProfile;
        private RibbonPageGroup ribbonPageGroupInitialResults;
        private BarEditItem barEditItemInitialResultsLimit;
        private RepositoryItemSpinEdit repositoryItemSpinEditInitialSearch;
        private Document document3;
        private RibbonPageGroup ribbonPageGroupIgnoreSeller;
        private RibbonPageGroup ribbonPageGroup1;
        private BarButtonItem barButtonItemIgnoreSellers;
        private DevExpress.XtraTreeList.Columns.TreeListColumn cEnabled;
        private RepositoryItemCheckEdit repositoryItemCheckEditEnabled;
        private RepositoryItemCheckEdit repositoryItemCheckEditFolder;
        private RepositoryItemCheckEdit repositoryItemCheckEditFolderOpen;
        private BarButtonItem barButtonItemRestoreBackup;
        private RibbonPageGroup ribbonPageGroup2;
        private BarButtonItem barButtonItemCreateBackup;
        private BarButtonItem barButtonItemChangelog;
        private RepositoryItemCheckEdit repositoryItemCheckEdit1;
        private BarCheckItem barCheckItemTotalPriceAsMaxPrice;
        private BarCheckItem barCheckItemPriceOpensCheckout;
        private BarButtonItem barButtonItemSync;
        private RibbonPage ribbonPageSync;
        private RibbonPageGroup ribbonPageGroupSync;
        private RibbonPage ribbonPage9;
        private DockPanel dockPanelExternalData;
        private ControlContainer controlContainer3;
        private BarButtonItem barButtonItemGetExternalData;
        private RibbonPageGroup ribbonPageGroupExternalData;
        private Document document8;
        private PanelControl panelCefBrowser;
        private LayoutControl layoutControlForPicture;
        private LayoutControlGroup layoutControlGroupPictures;
        private LayoutControlItem lcipanelPicturesControl;
        private LayoutControlItem lcipanelPicturesSettingControl;
        private LayoutControlItem lcipanelPicturesOpenSettingsControl;
        private RibbonPage ribbonPageData;
        private RibbonPage ribbonPage10;
        private Document document9;
        private DockPanel dockPanelWatchlist;
        private ControlContainer controlContainer7;
        private LayoutControl layoutWatchlistControls;
        private LayoutControlGroup layoutWatchlistRoot;
        private SimpleButton btnRefreshWatchlist;
        private LayoutControlItem lciWatchlist;
        private TimeSpanEdit timeSpanWatchlistRefreshInterval;
        private LayoutControlItem lciWatchlistRefreshInterval;
        private ToggleSwitch chkRefreshAndNotifyWatchlist;
        private LayoutControlItem lciTotalPrice;
        private LayoutControlItem lciConditionDescription;
        private LayoutControlItem lciCondition;
        private LayoutControlItem lciReturns;
        private LayoutControlItem lciTitle;
        internal LayoutControlItem lciSellerName;
        private LayoutControlItem lciLocation;
        private LayoutControlItem layoutControlItem1;
        private LayoutControlItem lciItemID;
        private LayoutControlItem lciFeedbackScore;
        private LayoutControlItem lciFeedbackRating;
        private LayoutControlItem lciQuantity;
        private LayoutControlItem lciAuctionPrice;
        private LayoutControlItem lciListingType;
        private LayoutControlItem lciBids;
        private LayoutControlItem layoutControlItem3;
        private LayoutControlItem layoutControlItem4;
        private EmptySpaceItem emptySpaceItem3;
        private SplitterItem splitterItem1;
        private SplitterItem splitterItem2;
        private HyperlinkLabelControl lnkWatchlistImportFromClipboard;
        private LayoutControlItem layoutControlItem5;
        public MemoEdit memoEditFilterLog;
        private DevExpress.XtraTab.XtraTabControl xtraTabLog;
        private DevExpress.XtraTab.XtraTabPage xtraTabPageFilterLog;
        private DevExpress.XtraTab.XtraTabPage xtraTabPageErrorLog;
        private PanelControl panelControlFilterLog;
        private SimpleButton btnClearFilterLog;
        private LayoutControl layoutControlCefBrowser;
        private LayoutControlGroup layoutControlGroupExternalData;
        private LayoutControlItem layoutControlItem2;
        private BarEditItem barEditItemResultsMaxCount;
        private RepositoryItemSpinEdit repositoryItemResultsMaxCount;
        private RibbonPageGroup ribbonPageGroupResultsLimit;
        private BarCheckItem barCheckItemRestock;
        private RibbonPageGroup ribbonPageGroupRestock;
        private BarButtonItem barButtonItemRestockReport;
        private BarStaticItem barStaticItemRestock;
        private BarEditItem barEditItemDailySpendLimit;
        private RepositoryItemSpinEdit repositoryItemSpinEditDailySpendLimit;
        private BarStaticItem barStaticItemTodaySpent;
        private SimpleButton btnCreateFolder;
    }
}
