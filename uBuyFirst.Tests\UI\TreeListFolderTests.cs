﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Search;
using uBuyFirst.SubSearch;

namespace uBuyFirst.Tests.UI
{
    [TestClass]
    public class TreeListFolderTests
    {
        private KeywordFolder _testFolder;
        private KeywordFolder _subFolder;
        private List<Keyword2Find> _keywords;

        [TestInitialize]
        public void Setup()
        {
            _testFolder = new KeywordFolder { Name = "Test Folder" };
            _subFolder = new KeywordFolder { Name = "Sub Folder", ParentFolder = _testFolder };
            _testFolder.Children.Add(_subFolder);

            _keywords = new List<Keyword2Find>
            {
                new Keyword2Find { Alias = "Keyword 1", KeywordEnabled = CheckState.Unchecked, ParentFolder = _testFolder },
                new Keyword2Find { Alias = "Keyword 2", KeywordEnabled = CheckState.Unchecked, ParentFolder = _testFolder },
                new Keyword2Find { Alias = "Keyword 3", KeywordEnabled = CheckState.Unchecked, ParentFolder = _subFolder }
            };

            _testFolder.Keywords.Add(_keywords[0]);
            _testFolder.Keywords.Add(_keywords[1]);
            _subFolder.Keywords.Add(_keywords[2]);
        }

        [TestMethod]
        public void CascadeFolderCheckState_EnableFolder_EnablesAllKeywords()
        {
            // Arrange - all keywords start as unchecked
            Assert.AreEqual(CheckState.Unchecked, _keywords[0].KeywordEnabled);
            Assert.AreEqual(CheckState.Unchecked, _keywords[1].KeywordEnabled);
            Assert.AreEqual(CheckState.Unchecked, _keywords[2].KeywordEnabled);

            // Act - simulate enabling the folder
            CascadeFolderCheckStateTest(_testFolder, CheckState.Checked);

            // Assert - all keywords should be enabled
            Assert.AreEqual(CheckState.Checked, _keywords[0].KeywordEnabled);
            Assert.AreEqual(CheckState.Checked, _keywords[1].KeywordEnabled);
            Assert.AreEqual(CheckState.Checked, _keywords[2].KeywordEnabled);
        }

        [TestMethod]
        public void CascadeFolderCheckState_DisableFolder_DisablesAllKeywords()
        {
            // Arrange - enable all keywords first
            _keywords[0].KeywordEnabled = CheckState.Checked;
            _keywords[1].KeywordEnabled = CheckState.Checked;
            _keywords[2].KeywordEnabled = CheckState.Checked;

            // Act - simulate disabling the folder
            CascadeFolderCheckStateTest(_testFolder, CheckState.Unchecked);

            // Assert - all keywords should be disabled
            Assert.AreEqual(CheckState.Unchecked, _keywords[0].KeywordEnabled);
            Assert.AreEqual(CheckState.Unchecked, _keywords[1].KeywordEnabled);
            Assert.AreEqual(CheckState.Unchecked, _keywords[2].KeywordEnabled);
        }

        [TestMethod]
        public void CascadeFolderCheckState_SubFolderOnly_AffectsOnlySubFolderKeywords()
        {
            // Arrange - all keywords start as unchecked
            Assert.AreEqual(CheckState.Unchecked, _keywords[0].KeywordEnabled);
            Assert.AreEqual(CheckState.Unchecked, _keywords[1].KeywordEnabled);
            Assert.AreEqual(CheckState.Unchecked, _keywords[2].KeywordEnabled);

            // Act - enable only the subfolder
            CascadeFolderCheckStateTest(_subFolder, CheckState.Checked);

            // Assert - only subfolder keyword should be enabled
            Assert.AreEqual(CheckState.Unchecked, _keywords[0].KeywordEnabled); // Parent folder keyword
            Assert.AreEqual(CheckState.Unchecked, _keywords[1].KeywordEnabled); // Parent folder keyword
            Assert.AreEqual(CheckState.Checked, _keywords[2].KeywordEnabled);   // Subfolder keyword
        }

        [TestMethod]
        public void CascadeFolderCheckState_EmptyFolder_DoesNotThrow()
        {
            // Arrange
            var emptyFolder = new KeywordFolder { Name = "Empty Folder" };

            // Act & Assert - should not throw exception
            try
            {
                CascadeFolderCheckStateTest(emptyFolder, CheckState.Checked);
                Assert.IsTrue(true, "No exception thrown for empty folder");
            }
            catch (Exception ex)
            {
                Assert.Fail($"Exception thrown for empty folder: {ex.Message}");
            }
        }

        [TestMethod]
        public void CascadeFolderCheckState_DeepNesting_CascadesToAllLevels()
        {
            // Arrange - create deeper nesting
            var deepFolder = new KeywordFolder { Name = "Deep Folder", ParentFolder = _subFolder };
            var deepKeyword = new Keyword2Find { Alias = "Deep Keyword", KeywordEnabled = CheckState.Unchecked, ParentFolder = deepFolder };

            _subFolder.Children.Add(deepFolder);
            deepFolder.Keywords.Add(deepKeyword);

            // Act - enable the root folder
            CascadeFolderCheckStateTest(_testFolder, CheckState.Checked);

            // Assert - all keywords at all levels should be enabled
            Assert.AreEqual(CheckState.Checked, _keywords[0].KeywordEnabled); // Level 1
            Assert.AreEqual(CheckState.Checked, _keywords[1].KeywordEnabled); // Level 1
            Assert.AreEqual(CheckState.Checked, _keywords[2].KeywordEnabled); // Level 2
            Assert.AreEqual(CheckState.Checked, deepKeyword.KeywordEnabled);  // Level 3
        }

        [TestMethod]
        public void FolderEnabledState_AllEnabled_ReturnsChecked()
        {
            // Arrange
            _keywords[0].KeywordEnabled = CheckState.Checked;
            _keywords[1].KeywordEnabled = CheckState.Checked;
            _keywords[2].KeywordEnabled = CheckState.Checked;

            // Act
            var enabledState = GetFolderEnabledStateTest(_testFolder);

            // Assert
            Assert.AreEqual(CheckState.Checked, enabledState);
        }

        [TestMethod]
        public void FolderEnabledState_AllDisabled_ReturnsUnchecked()
        {
            // Arrange - keywords are already unchecked from setup

            // Act
            var enabledState = GetFolderEnabledStateTest(_testFolder);

            // Assert
            Assert.AreEqual(CheckState.Unchecked, enabledState);
        }

        [TestMethod]
        public void FolderEnabledState_Mixed_ReturnsIndeterminate()
        {
            // Arrange
            _keywords[0].KeywordEnabled = CheckState.Checked;   // Enabled
            _keywords[1].KeywordEnabled = CheckState.Unchecked; // Disabled
            _keywords[2].KeywordEnabled = CheckState.Checked;   // Enabled

            // Act
            var enabledState = GetFolderEnabledStateTest(_testFolder);

            // Assert
            Assert.AreEqual(CheckState.Indeterminate, enabledState);
        }

        #region Helper Methods

        /// <summary>
        /// Test helper that simulates the CascadeFolderCheckState method from Form1.Treelist.cs
        /// </summary>
        private void CascadeFolderCheckStateTest(KeywordFolder folder, CheckState checkState)
        {
            // Update all keywords in this folder
            foreach (var keyword in folder.Keywords)
            {
                keyword.KeywordEnabled = checkState;
            }

            // Recursively update all subfolders and their contents
            foreach (var childFolder in folder.Children)
            {
                CascadeFolderCheckStateTest(childFolder, checkState);
            }
        }

        /// <summary>
        /// Test helper that simulates getting folder enabled state
        /// </summary>
        private CheckState GetFolderEnabledStateTest(KeywordFolder folder)
        {
            var allKeywords = folder.GetAllKeywords();

            if (!allKeywords.Any())
                return CheckState.Unchecked;

            var enabledCount = allKeywords.Count(k => k.KeywordEnabled == CheckState.Checked);
            var totalCount = allKeywords.Count;

            if (enabledCount == 0)
                return CheckState.Unchecked;
            else if (enabledCount == totalCount)
                return CheckState.Checked;
            else
                return CheckState.Indeterminate;
        }

        [TestMethod]
        public void SetNodeChecked_Folder_SetsCorrectCheckState()
        {
            // Arrange - mixed keyword states
            _keywords[0].KeywordEnabled = CheckState.Checked;   // Enabled
            _keywords[1].KeywordEnabled = CheckState.Unchecked; // Disabled
            _keywords[2].KeywordEnabled = CheckState.Checked;   // Enabled

            // Act
            var checkState = GetFolderCheckStateTest(_testFolder);

            // Assert - should be indeterminate (mixed states)
            Assert.AreEqual(CheckState.Indeterminate, checkState);
        }

        [TestMethod]
        public void RepositoryItemCheckEdit_FolderClick_CascadesToAllKeywords()
        {
            // Arrange - all keywords start as unchecked
            Assert.AreEqual(CheckState.Unchecked, _keywords[0].KeywordEnabled);
            Assert.AreEqual(CheckState.Unchecked, _keywords[1].KeywordEnabled);
            Assert.AreEqual(CheckState.Unchecked, _keywords[2].KeywordEnabled);

            // Act - simulate repository item checkbox click for folder
            SimulateRepositoryItemCheckboxClick(_testFolder, true);

            // Assert - all keywords should be enabled
            Assert.AreEqual(CheckState.Checked, _keywords[0].KeywordEnabled);
            Assert.AreEqual(CheckState.Checked, _keywords[1].KeywordEnabled);
            Assert.AreEqual(CheckState.Checked, _keywords[2].KeywordEnabled);
        }

        /// <summary>
        /// Test helper that simulates the SetNodeChecked method from Form1.EBaySearches.cs
        /// </summary>
        private CheckState GetFolderCheckStateTest(KeywordFolder folder)
        {
            var allKeywords = folder.GetAllKeywords();
            if (!allKeywords.Any())
                return CheckState.Unchecked;

            var enabledCount = allKeywords.Count(k => k.KeywordEnabled == CheckState.Checked);
            var totalCount = allKeywords.Count;

            if (enabledCount == 0)
                return CheckState.Unchecked;
            else if (enabledCount == totalCount)
                return CheckState.Checked;
            else
                return CheckState.Indeterminate;
        }

        /// <summary>
        /// Test helper that simulates the repositoryItemCheckEditEnabled_EditValueChanged method
        /// </summary>
        private void SimulateRepositoryItemCheckboxClick(object dataRecord, bool isChecked)
        {
            var checkState = isChecked ? CheckState.Checked : CheckState.Unchecked;

            switch (dataRecord)
            {
                case Keyword2Find kw:
                    kw.KeywordEnabled = checkState;
                    break;
                case ChildTerm childTerm:
                    childTerm.Enabled = isChecked;
                    break;
                case KeywordFolder folder:
                    // Cascade enable/disable to all keywords and subfolders
                    CascadeFolderCheckStateTest(folder, checkState);
                    break;
            }
        }

        #endregion

        [TestMethod]
        public void CustomDrawNodeCell_FolderNode_ShouldDrawFolderIcon()
        {
            // This test verifies that the CustomDrawNodeCell event handler
            // correctly identifies folder nodes and attempts to draw folder icons
            // Note: This is a conceptual test since we can't easily test UI drawing

            // Arrange
            var folder = new KeywordFolder { Name = "Test Folder" };

            // Act & Assert
            // The actual drawing logic is tested through manual verification
            // The key logic is that folder nodes are identified correctly
            Assert.IsNotNull(folder);
            Assert.AreEqual("Test Folder", folder.Name);

            // The CustomDrawNodeCell method should:
            // 1. Check if column is "Enabled"
            // 2. Check if dataRecord is KeywordFolder
            // 3. Select appropriate icon based on expansion state (Folder vs FolderOpen)
            // 4. Use SvgBitmap.Create() and Render() to convert SVG to bitmap
            // 5. Draw the icon in the correct position using Graphics.DrawImage()
        }

        [TestMethod]
        public void FolderIcon_Resources_ShouldBeAvailable()
        {
            // Verify that the folder icon resources are available
            Assert.IsNotNull(Properties.Resources.Folder, "Folder.svg resource should be available");
            Assert.IsNotNull(Properties.Resources.FolderOpen, "FolderOpen.svg resource should be available");
        }
    }
}
