# CSV Export/Import Specification

## 📋 Overview

This document defines the CSV format and implementation for exporting and importing keywords with folder structure support.

## 📊 CSV Format Specification

### Header Structure
```csv
Id,Folder Path,Sub Search Id,eBay Search Alias,Sub Search Alias,Keywords,Keyword enabled,Search in Description,Price Min,Price Max,Category ID,Condition,Site,Located in,Ships to,Ship Zipcode,Sellers,Include/Exclude,Interval,Listing Type,Filter,View,Job ID,Required Quantity
```

### New Columns
- **Folder Path**: Human-readable folder hierarchy (e.g., "Electronics > Mobile Phones > Apple")

### Column Details

| Column | Type | Description | Example |
|--------|------|-------------|---------|
| Folder Path | String | Full folder path with " > " separator | "Electronics > Mobile Phones" |
| Id | String | Keyword unique identifier | "kw-12345" |
| Sub Search Id | String | Child term identifier (empty for main keyword) | "sub-67890" |
| eBay Search Alias | String | Main keyword alias | "iPhone Search" |
| Sub Search Alias | String | Child term alias (empty for main keyword) | "iPhone Pro" |

## 📤 Export Implementation

### Updated Export Method

```csharp
public static void ExportSearchesToFile(List<Keyword2Find> searchTermList)
{
    try
    {
        var header = new List<string>
        {
            "Id",
            "Folder Path",          // NEW: Folder hierarchy
            "Sub Search Id",
            "eBay Search Alias",
            "Sub Search Alias",
            "Keywords",
            "Keyword enabled",
            "Search in Description",
            "Price Min",
            "Price Max",
            "Category ID",
            "Condition",
            "Site",
            "Located in",
            "Ships to",
            "Ship Zipcode",
            "Sellers",
            "Include/Exclude",
            "Interval",
            "Listing Type",
            "Filter",
            "View"
        };

        // Add Restocker fields only if RestockerEnabled is true
        if (ConnectionConfig.RestockerEnabled)
        {
            header.Add("Job ID");
            header.Add("Required Quantity");
        }

        var fileContents = Helpers.CreateCSVRow(header) + "\r\n";
        foreach (var item in searchTermList)
        {
            fileContents += Export(item);
        }

        File.WriteAllText(Path.Combine(Folders.Logs, "KeywordsExport.csv"), fileContents);
        Process.Start(Path.Combine(Folders.Logs, "KeywordsExport.csv"));
    }
    catch (IOException ex)
    {
        XtraMessageBox.Show("Please, close all spreadsheet documents and try again.\n" + ex.Message);
    }
    catch (Exception ex)
    {
        ExM.ubuyExceptionHandler("Export keywords: ", ex);
    }
}
```

### Updated Export Method for Individual Keywords

```csharp
public static string Export(Keyword2Find kw)
{
    var folderPath = BuildFolderPath(kw.ParentFolder);
    var conditionsEbaySearch = "";
    
    foreach (var c in kw.Condition)
    {
        if (SearchStuff.ConditionsDict.TryGetValue(c, out var s))
            conditionsEbaySearch += s + ",";
    }

    conditionsEbaySearch = conditionsEbaySearch.Trim(',');
    
    var kwProperties = new List<string>
    {
        kw.Id,
        folderPath,                 // NEW: Folder path
        "",                         // Sub Search Id (empty for main keyword)
        kw.Alias,
        "",                         // Sub Search Alias (empty for main keyword)
        kw.Kws,
        (kw.KeywordEnabled == CheckState.Checked).ToString(),
        kw.SearchInDescription.ToString(),
        kw.PriceMin.ToString(CultureInfo.InvariantCulture),
        kw.PriceMax.ToString(CultureInfo.InvariantCulture),
        string.Join("|", kw.Categories4Api.Split(',')),
        conditionsEbaySearch,
        kw.EBaySite.ToString(),
        kw.LocatedIn,
        kw.AvailableTo,
        kw.Zip,
        kw.SellersStr,
        kw.SellerType,
        kw.Frequency.ToString(@"hh\:mm\:ss"),
        string.Join(",", kw.ListingType),
        "",                         // Filter (empty for main keyword)
        kw.ViewName
    };

    // Add Restocker field values only if RestockerEnabled is true
    if (ConnectionConfig.RestockerEnabled)
    {
        kwProperties.Add(kw.JobId);
        kwProperties.Add(kw.RequiredQuantity.ToString(CultureInfo.InvariantCulture));
    }

    var row = Helpers.CreateCSVRow(kwProperties) + "\r\n";

    // Export child terms (sub-searches)
    foreach (var term in kw.ChildrenCore)
    {
        var conditionsSubSearch = "";
        foreach (var c in term.Condition)
        {
            if (SearchStuff.ConditionsDict.TryGetValue(c, out var s))
                conditionsSubSearch += s + ",";
        }

        conditionsSubSearch = conditionsSubSearch.Trim(',');
        var subFilter = term.SubSearch?.FilterCriteria?.ToString() ?? "";
        
        var childRow = new List<string>
        {
            kw.Id,
            folderPath,             // Same folder path as parent keyword
            term.Id,                // Sub Search Id
            kw.Alias,               // Parent keyword alias
            term.Alias,             // Sub Search Alias
            term.Keywords,
            term.Enabled.ToString(),
            term.SearchInDescription.ToString(),
            term.PriceMin.ToString(CultureInfo.InvariantCulture),
            term.PriceMax.ToString(CultureInfo.InvariantCulture),
            string.Join("|", term.CategoryIDs),
            conditionsSubSearch,
            "",                     // Site (empty for child terms)
            "",                     // Located in (empty for child terms)
            "",                     // Ships to (empty for child terms)
            "",                     // Ship Zipcode (empty for child terms)
            "",                     // Sellers (empty for child terms)
            "",                     // Include/Exclude (empty for child terms)
            "",                     // Interval (empty for child terms)
            "",                     // Listing Type (empty for child terms)
            subFilter,              // Filter
            ""                      // View (empty for child terms)
        };

        // Add empty Restocker field values for child rows only if RestockerEnabled is true
        if (ConnectionConfig.RestockerEnabled)
        {
            childRow.Add(""); // Job ID
            childRow.Add(""); // Required Quantity
        }
        
        row += Helpers.CreateCSVRow(childRow) + "\r\n";
    }

    return row;
}

/// <summary>
/// Builds the folder path string for a keyword
/// </summary>
private static string BuildFolderPath(KeywordFolder folder)
{
    if (folder == null) return "";
    
    var pathParts = new List<string>();
    var current = folder;
    
    while (current != null)
    {
        pathParts.Insert(0, current.Name);
        current = current.ParentFolder;
    }
    
    return string.Join(" > ", pathParts);
}
```

## 📥 Import Implementation

### Updated Import Method

```csharp
public static string Import(List<string> cells, Keyword2Find kw, Dictionary<string, int> columnMapping)
{
    var importLog = "";
    try
    {
        kw.Threads = 1;

        // Existing import logic for all other fields...
        var idValue = GetCellValue(cells, columnMapping, "Id");
        if (!string.IsNullOrEmpty(idValue))
            kw.Id = idValue;
        else
            importLog += "Id, ";

        var aliasValue = GetCellValue(cells, columnMapping, "eBay Search Alias");
        if (!string.IsNullOrEmpty(aliasValue))
            kw.Alias = aliasValue;
        else
            importLog += "Alias, ";

        // NEW: Handle folder information
        var folderPath = GetCellValue(cells, columnMapping, "Folder Path");
        if (!string.IsNullOrEmpty(folderPath))
        {
            var folder = FindOrCreateFolderFromPath(folderPath);
            kw.ParentFolder = folder;
            folder?.Keywords.Add(kw);
        }
        // If no folder path, keyword will remain at root level

        // Continue with existing import logic for other fields...
        
    }
    catch (Exception ex)
    {
        importLog += "Exception: " + ex.Message;
    }

    return importLog;
}

/// <summary>
/// Finds or creates folder hierarchy from path string
/// </summary>
private static KeywordFolder FindOrCreateFolderFromPath(string folderPath)
{
    if (string.IsNullOrEmpty(folderPath)) return null;
    
    // Split path: "Electronics > Mobile Phones > Apple" -> ["Electronics", "Mobile Phones", "Apple"]
    var pathParts = folderPath.Split(new[] { " > " }, StringSplitOptions.RemoveEmptyEntries);
    
    KeywordFolder currentFolder = null;
    
    // Create/find each folder level
    foreach (var folderName in pathParts)
    {
        currentFolder = FindOrCreateChildFolder(currentFolder, folderName.Trim());
    }
    
    return currentFolder;
}

/// <summary>
/// Finds or creates a child folder with the specified name
/// </summary>
private static KeywordFolder FindOrCreateChildFolder(KeywordFolder parent, string folderName)
{
    // Validate folder name
    if (string.IsNullOrWhiteSpace(folderName))
        throw new ArgumentException("Folder name cannot be empty");
    
    // Find existing child folder (case-insensitive)
    KeywordFolder existingFolder = null;
    
    if (parent == null)
    {
        // Search in root folders
        existingFolder = _queryList.Folders.FirstOrDefault(f => 
            f.Name.Equals(folderName, StringComparison.OrdinalIgnoreCase));
    }
    else
    {
        // Search in parent's children
        existingFolder = parent.Children.FirstOrDefault(f => 
            f.Name.Equals(folderName, StringComparison.OrdinalIgnoreCase));
    }
    
    if (existingFolder != null)
        return existingFolder;
    
    // Create new folder
    var newFolder = new KeywordFolder 
    { 
        Name = folderName, 
        ParentFolder = parent,
        Id = Guid.NewGuid().ToString()
    };
    
    if (parent == null)
    {
        // Add to root folders
        _queryList.Folders.Add(newFolder);
    }
    else
    {
        // Add to parent's children
        parent.Children.Add(newFolder);
    }
    
    return newFolder;
}
```

## 📋 CSV Examples

### Example 1: Simple Folder Structure
```csv
Id,Folder Path,Sub Search Id,eBay Search Alias,Sub Search Alias,Keywords,Keyword enabled,Price Min,Price Max
kw1,Electronics,,iPhone Search,,iPhone 14,true,100,1000
kw1,Electronics,sub1,iPhone Search,iPhone Pro,iPhone 14 Pro,true,200,1200
kw2,Electronics,,Samsung Search,,Galaxy S24,true,300,800
kw3,Clothing,,Shoes,,Nike Air,true,50,300
```

### Example 2: Multi-Level Folder Structure
```csv
Id,Folder Path,Sub Search Id,eBay Search Alias,Sub Search Alias,Keywords,Keyword enabled,Price Min,Price Max
kw1,Electronics > Mobile Phones,,iPhone Search,,iPhone 14,true,100,1000
kw2,Electronics > Mobile Phones > Apple,,iPhone Pro Search,,iPhone 15 Pro,true,800,1500
kw3,Electronics > Computers,,Laptop Search,,ThinkPad,true,400,1200
kw4,Clothing > Footwear > Sneakers,,Running Shoes,,Adidas Ultra,true,80,250
kw5,,,Uncategorized Search,,Random stuff,true,1,1000
```

### Example 3: With Restocker Fields
```csv
Id,Folder Path,Sub Search Id,eBay Search Alias,Sub Search Alias,Keywords,Keyword enabled,Job ID,Required Quantity
kw1,Electronics,,iPhone Search,,iPhone 14,true,JOB-001,5
kw2,Electronics > Mobile Phones,,Samsung Search,,Galaxy S24,true,JOB-002,10
```

## 🔍 Validation Rules

### Folder Path Validation
- **Empty Path**: Valid - keyword goes to root level
- **Invalid Characters**: Reject paths with control characters
- **Path Length**: Maximum 500 characters total
- **Folder Name Length**: Maximum 100 characters per folder name
- **Nesting Depth**: Maximum 10 levels deep

### Import Validation
```csharp
private static bool ValidateFolderPath(string folderPath, out string errorMessage)
{
    errorMessage = "";
    
    if (string.IsNullOrEmpty(folderPath))
        return true; // Empty path is valid
    
    if (folderPath.Length > 500)
    {
        errorMessage = "Folder path too long (maximum 500 characters)";
        return false;
    }
    
    var pathParts = folderPath.Split(new[] { " > " }, StringSplitOptions.RemoveEmptyEntries);
    
    if (pathParts.Length > 10)
    {
        errorMessage = "Folder nesting too deep (maximum 10 levels)";
        return false;
    }
    
    foreach (var part in pathParts)
    {
        if (string.IsNullOrWhiteSpace(part))
        {
            errorMessage = "Empty folder name in path";
            return false;
        }
        
        if (part.Length > 100)
        {
            errorMessage = $"Folder name too long: '{part}' (maximum 100 characters)";
            return false;
        }
        
        if (part.IndexOfAny(Path.GetInvalidFileNameChars()) >= 0)
        {
            errorMessage = $"Invalid characters in folder name: '{part}'";
            return false;
        }
    }
    
    return true;
}
```

## 🔄 Backward Compatibility

### Importing Old CSV Files
- CSV files without "Folder Path" column import successfully
- Keywords without folder information go to root level
- All existing functionality remains intact

### Migration Strategy
1. **Detect Format**: Check if "Folder Path" column exists
2. **Handle Missing Column**: Gracefully handle missing folder information
3. **Default Behavior**: Keywords without folders remain at root level
4. **Preserve Data**: All existing keyword data is preserved

## 🧪 Testing Scenarios

### Export Testing
- [ ] Export keywords with no folders
- [ ] Export keywords with single-level folders
- [ ] Export keywords with multi-level folders
- [ ] Export with special characters in folder names
- [ ] Export large datasets (1000+ keywords)

### Import Testing
- [ ] Import CSV without folder column (backward compatibility)
- [ ] Import CSV with folder paths
- [ ] Import with invalid folder paths
- [ ] Import with duplicate folder names
- [ ] Import with circular references (should be prevented)

### Edge Cases
- [ ] Empty folder paths
- [ ] Very long folder paths
- [ ] Special characters in folder names
- [ ] Unicode characters in folder names
- [ ] Malformed CSV files
