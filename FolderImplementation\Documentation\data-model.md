# Data Model Specification

## 📊 Overview

This document defines the data structures and relationships for the folder functionality in the keywords TreeList system.

## 🏗️ Class Hierarchy

```
QueryList (Root)
├── List<KeywordFolder> Folders
    ├── KeywordFolder (Level 0)
    │   ├── Properties (Id, Name, IsExpanded, etc.)
    │   ├── KeywordFolder ParentFolder
    │   ├── List<KeywordFolder> Children (nested folders)
    │   └── List<Keyword2Find> Keywords
    │       ├── Keyword2Find (Level 1)
    │       │   ├── Properties (Alias, Keywords, etc.)
    │       │   ├── KeywordFolder ParentFolder
    │       │   └── List<ChildTerm> ChildrenCore
    │       │       └── ChildTerm (Level 2)
    │       └── ...
    └── ...
```

## 📋 Class Definitions

### KeywordFolder Class

```csharp
[Serializable]
[XmlInclude(typeof(Keyword2Find))]
public class KeywordFolder : IVirtualTreeListData
{
    #region Core Properties
    
    /// <summary>
    /// Unique identifier for the folder
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    /// <summary>
    /// Display name of the folder
    /// </summary>
    public string Name { get; set; }
    
    /// <summary>
    /// Whether the folder is expanded in the TreeList
    /// </summary>
    public bool IsExpanded { get; set; } = true;
    
    /// <summary>
    /// Optional description for the folder
    /// </summary>
    public string Description { get; set; } = "";
    
    /// <summary>
    /// Creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    #endregion
    
    #region Hierarchy Relationships
    
    /// <summary>
    /// Parent folder (null for root-level folders)
    /// </summary>
    [XmlIgnore]
    public KeywordFolder ParentFolder { get; set; }
    
    /// <summary>
    /// Child folders (nested folders)
    /// </summary>
    public List<KeywordFolder> Children { get; set; } = new List<KeywordFolder>();
    
    /// <summary>
    /// Keywords contained in this folder
    /// </summary>
    public List<Keyword2Find> Keywords { get; set; } = new List<Keyword2Find>();
    
    #endregion
    
    #region TreeList Integration
    
    /// <summary>
    /// Provides child nodes for TreeList virtual mode
    /// </summary>
    public void VirtualTreeGetChildNodes(VirtualTreeGetChildNodesInfo info)
    {
        var children = new List<object>();
        children.AddRange(Children);      // Add child folders first
        children.AddRange(Keywords);      // Then add keywords
        info.Children = children;
    }
    
    /// <summary>
    /// Provides cell values for TreeList columns
    /// </summary>
    public void VirtualTreeGetCellValue(VirtualTreeGetCellValueInfo info)
    {
        switch (info.Column.Caption)
        {
            case "Enabled":
                info.CellData = true; // Folders are always "enabled"
                break;
            case "Alias":
                info.CellData = Name;
                break;
            case "Keywords":
                info.CellData = $"[{Keywords.Count} keywords]";
                break;
            default:
                info.CellData = ""; // Empty for other columns
                break;
        }
    }
    
    /// <summary>
    /// Handles cell value changes (e.g., folder rename)
    /// </summary>
    public void VirtualTreeSetCellValue(VirtualTreeSetCellValueInfo info)
    {
        if (info.Column.Caption == "Alias" && info.NewCellData is string newName)
        {
            Name = newName;
        }
    }
    
    #endregion
    
    #region Utility Methods
    
    /// <summary>
    /// Gets the full path of the folder (e.g., "Electronics > Mobile Phones")
    /// </summary>
    public string GetFullPath()
    {
        if (ParentFolder == null) return Name;
        
        var pathParts = new List<string>();
        var current = this;
        
        while (current != null)
        {
            pathParts.Insert(0, current.Name);
            current = current.ParentFolder;
        }
        
        return string.Join(" > ", pathParts);
    }
    
    /// <summary>
    /// Gets all keywords in this folder and all subfolders
    /// </summary>
    public List<Keyword2Find> GetAllKeywords()
    {
        var allKeywords = new List<Keyword2Find>(Keywords);
        
        foreach (var childFolder in Children)
        {
            allKeywords.AddRange(childFolder.GetAllKeywords());
        }
        
        return allKeywords;
    }
    
    /// <summary>
    /// Gets all folders in this hierarchy (including this folder)
    /// </summary>
    public List<KeywordFolder> GetAllFolders()
    {
        var allFolders = new List<KeywordFolder> { this };
        
        foreach (var childFolder in Children)
        {
            allFolders.AddRange(childFolder.GetAllFolders());
        }
        
        return allFolders;
    }
    
    /// <summary>
    /// Checks if this folder can accept a dropped item
    /// </summary>
    public bool CanAcceptDrop(object draggedItem)
    {
        switch (draggedItem)
        {
            case Keyword2Find keyword:
                return true; // Folders can always accept keywords
            case KeywordFolder folder:
                return !folder.IsAncestorOf(this); // Prevent circular references
            default:
                return false;
        }
    }
    
    /// <summary>
    /// Handles dropping an item into this folder
    /// </summary>
    public void AcceptDrop(object draggedItem, int position = -1)
    {
        switch (draggedItem)
        {
            case Keyword2Find keyword:
                // Remove from current parent
                keyword.ParentFolder?.Keywords.Remove(keyword);
                
                // Add to this folder
                if (position >= 0 && position < Keywords.Count)
                    Keywords.Insert(position, keyword);
                else
                    Keywords.Add(keyword);
                
                keyword.ParentFolder = this;
                break;
                
            case KeywordFolder folder:
                if (CanAcceptDrop(folder))
                {
                    // Remove from current parent
                    folder.ParentFolder?.Children.Remove(folder);
                    
                    // Add to this folder
                    if (position >= 0 && position < Children.Count)
                        Children.Insert(position, folder);
                    else
                        Children.Add(folder);
                    
                    folder.ParentFolder = this;
                }
                break;
        }
    }
    
    /// <summary>
    /// Checks if this folder is an ancestor of the specified folder
    /// </summary>
    private bool IsAncestorOf(KeywordFolder folder)
    {
        var current = folder.ParentFolder;
        while (current != null)
        {
            if (current == this) return true;
            current = current.ParentFolder;
        }
        return false;
    }
    
    /// <summary>
    /// Validates the folder hierarchy for circular references
    /// </summary>
    public bool ValidateHierarchy()
    {
        var visited = new HashSet<string>();
        return ValidateHierarchyRecursive(visited);
    }
    
    private bool ValidateHierarchyRecursive(HashSet<string> visited)
    {
        if (visited.Contains(Id)) return false; // Circular reference detected
        
        visited.Add(Id);
        
        foreach (var child in Children)
        {
            if (!child.ValidateHierarchyRecursive(new HashSet<string>(visited)))
                return false;
        }
        
        return true;
    }
    
    #endregion
}
```

### Modified QueryList Class

```csharp
public class QueryList : TreeList.IVirtualTreeListData, IXmlSerializable
{
    #region Properties
    
    /// <summary>
    /// Root-level folders
    /// </summary>
    public readonly List<KeywordFolder> Folders = new List<KeywordFolder>();
    
    /// <summary>
    /// Backward compatibility property - returns all keywords from all folders
    /// </summary>
    [XmlIgnore]
    public List<Keyword2Find> ChildrenCore 
    { 
        get { return GetAllKeywords(); }
    }
    
    #endregion
    
    #region TreeList Integration
    
    /// <summary>
    /// Provides root nodes for TreeList (folders)
    /// </summary>
    public void VirtualTreeGetChildNodes(VirtualTreeGetChildNodesInfo info)
    {
        info.Children = Folders;
    }
    
    public void VirtualTreeGetCellValue(VirtualTreeGetCellValueInfo info)
    {
        // Not used at root level
    }
    
    public void VirtualTreeSetCellValue(VirtualTreeSetCellValueInfo info)
    {
        // Not used at root level
    }
    
    #endregion
    
    #region Utility Methods
    
    /// <summary>
    /// Gets all keywords from all folders
    /// </summary>
    private List<Keyword2Find> GetAllKeywords()
    {
        var allKeywords = new List<Keyword2Find>();
        
        foreach (var folder in Folders)
        {
            allKeywords.AddRange(folder.GetAllKeywords());
        }
        
        return allKeywords;
    }
    
    /// <summary>
    /// Migrates from flat keyword structure to folder structure
    /// </summary>
    public void MigrateFromFlatStructure(List<Keyword2Find> existingKeywords)
    {
        if (Folders.Count > 0 || !existingKeywords.Any()) return;
        
        // Create default folder for existing keywords
        var defaultFolder = new KeywordFolder 
        { 
            Name = "Default",
            Id = "default-folder"
        };
        
        foreach (var keyword in existingKeywords)
        {
            keyword.ParentFolder = defaultFolder;
            defaultFolder.Keywords.Add(keyword);
        }
        
        Folders.Add(defaultFolder);
    }
    
    /// <summary>
    /// Finds a folder by its full path
    /// </summary>
    public KeywordFolder FindFolderByPath(string folderPath)
    {
        if (string.IsNullOrEmpty(folderPath)) return null;
        
        var pathParts = folderPath.Split(new[] { " > " }, StringSplitOptions.RemoveEmptyEntries);
        
        foreach (var rootFolder in Folders)
        {
            var found = FindFolderByPathRecursive(rootFolder, pathParts, 0);
            if (found != null) return found;
        }
        
        return null;
    }
    
    private KeywordFolder FindFolderByPathRecursive(KeywordFolder folder, string[] pathParts, int index)
    {
        if (index >= pathParts.Length) return null;
        
        if (folder.Name.Equals(pathParts[index], StringComparison.OrdinalIgnoreCase))
        {
            if (index == pathParts.Length - 1) return folder; // Found target folder
            
            // Continue searching in children
            foreach (var child in folder.Children)
            {
                var found = FindFolderByPathRecursive(child, pathParts, index + 1);
                if (found != null) return found;
            }
        }
        
        return null;
    }
    
    #endregion
    
    #region XML Serialization
    
    public XmlSchema GetSchema() => null;
    
    public void ReadXml(XmlReader reader)
    {
        // Implementation for reading XML
    }
    
    public void WriteXml(XmlWriter writer)
    {
        // Implementation for writing XML
    }
    
    #endregion
}
```

### Enhanced Keyword2Find Class

```csharp
public class Keyword2Find : TreeList.IVirtualTreeListData
{
    // All existing properties remain unchanged
    
    #region New Folder Properties
    
    /// <summary>
    /// Parent folder containing this keyword
    /// </summary>
    [XmlIgnore]
    public KeywordFolder ParentFolder { get; set; }
    
    /// <summary>
    /// Backward compatibility property for existing code
    /// </summary>
    [XmlIgnore]
    public QueryList ParentCore 
    { 
        get { return ParentFolder?.GetRootQueryList(); }
        set { /* Handle legacy assignment if needed */ }
    }
    
    #endregion
    
    // All existing methods remain unchanged
    // VirtualTreeGetChildNodes, VirtualTreeGetCellValue, etc.
}
```

## 🔗 Relationships

### Parent-Child Relationships
- **QueryList** → **KeywordFolder** (1:N)
- **KeywordFolder** → **KeywordFolder** (1:N, for nesting)
- **KeywordFolder** → **Keyword2Find** (1:N)
- **Keyword2Find** → **ChildTerm** (1:N, existing)

### Navigation Properties
- Each folder knows its parent and children
- Each keyword knows its parent folder
- Root QueryList provides access to all folders

### Constraints
- No circular references in folder hierarchy
- Folder names must be unique within the same parent
- Keywords can exist without folders (root level)

## 💾 Serialization Format

### XML Structure
```xml
<QueryList>
  <Folders>
    <KeywordFolder>
      <Id>electronics-folder</Id>
      <Name>Electronics</Name>
      <IsExpanded>true</IsExpanded>
      <Children>
        <KeywordFolder>
          <Id>mobile-phones-folder</Id>
          <Name>Mobile Phones</Name>
          <Keywords>
            <Keyword2Find>
              <Id>iphone-search</Id>
              <Alias>iPhone Search</Alias>
              <!-- ... other keyword properties ... -->
            </Keyword2Find>
          </Keywords>
        </KeywordFolder>
      </Children>
    </KeywordFolder>
  </Folders>
</QueryList>
```

### Backward Compatibility
- Old configurations without folders load into default folder
- Existing keyword properties remain unchanged
- Migration is automatic and transparent
